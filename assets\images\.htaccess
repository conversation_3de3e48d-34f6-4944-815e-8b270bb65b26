# Protect images from hotlinking
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Allow access from our domain only
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^http://localhost/dopminer [NC]
    RewriteCond %{HTTP_REFERER} !^https://localhost/dopminer [NC]
    RewriteCond %{HTTP_REFERER} !^http://127\.0\.0\.1/dopminer [NC]
    RewriteCond %{HTTP_REFERER} !^https://127\.0\.0\.1/dopminer [NC]
    RewriteRule \.(png|jpg|jpeg|gif|svg)$ ../blocked.html [R=403,L]
</IfModule>

# Disable directory browsing
Options -Indexes

# Cache control for images
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/png "access plus 1 week"
    ExpiresByType image/jpg "access plus 1 week"
    ExpiresByType image/jpeg "access plus 1 week"
    ExpiresByType image/gif "access plus 1 week"
    ExpiresByType image/svg+xml "access plus 1 week"
</IfModule> 