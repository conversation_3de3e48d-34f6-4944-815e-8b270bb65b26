<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Ticket extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('Ticket_model');
        $this->load->model('User_model');
        $this->load->model('Server_model');
        $this->load->library('session');
        
        // Check if user is logged in
        if (!$this->session->userdata('user_id')) {
            redirect('auth/login');
        }
    }

    public function index() {
        $data['title'] = 'Support Tickets';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        
        // Pagination
        $this->load->library('pagination');
        $config['base_url'] = base_url('ticket');
        $config['total_rows'] = $this->Ticket_model->count_user_tickets($this->session->userdata('user_id'));
        $config['per_page'] = 10;
        $config['uri_segment'] = 2;
        
        $this->pagination->initialize($config);
        
        $page = ($this->uri->segment(2)) ? $this->uri->segment(2) : 0;
        $data['tickets'] = $this->Ticket_model->get_tickets($this->session->userdata('user_id'), null, $config['per_page'], $page);
        $data['pagination'] = $this->pagination->create_links();
        
        // Get ticket statistics
        $data['stats'] = $this->Ticket_model->get_ticket_stats($this->session->userdata('user_id'));
        
        $this->load->view('includes/header', $data);
        $this->load->view('ticket/index', $data);
        $this->load->view('includes/footer');
    }

    public function create() {
        $data['title'] = 'Create Support Ticket';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        
        // Get user's orders for dropdown
        $data['orders'] = $this->Server_model->get_orders($this->session->userdata('user_id'));
        
        if ($this->input->method() === 'post') {
            $this->form_validation->set_rules('subject', 'Subject', 'required|min_length[5]|max_length[200]');
            $this->form_validation->set_rules('message', 'Message', 'required|min_length[10]');
            $this->form_validation->set_rules('priority', 'Priority', 'required|in_list[low,normal,high,urgent]');
            $this->form_validation->set_rules('category', 'Category', 'required|in_list[general,billing,technical,server_issue,abuse]');
            
            if ($this->form_validation->run()) {
                // Create ticket
                $ticket_data = [
                    'user_id' => $this->session->userdata('user_id'),
                    'order_id' => $this->input->post('order_id') ?: null,
                    'subject' => $this->input->post('subject'),
                    'priority' => $this->input->post('priority'),
                    'category' => $this->input->post('category'),
                    'status' => 'open'
                ];
                
                $ticket_id = $this->Ticket_model->create_ticket($ticket_data);
                
                if ($ticket_id) {
                    // Add initial message
                    $message_data = [
                        'ticket_id' => $ticket_id,
                        'user_id' => $this->session->userdata('user_id'),
                        'message' => $this->input->post('message'),
                        'is_internal' => 0
                    ];
                    
                    $this->Ticket_model->add_message($message_data);
                    
                    $this->session->set_flashdata('success', 'Support ticket created successfully!');
                    redirect('ticket/view/' . $ticket_id);
                } else {
                    $this->session->set_flashdata('error', 'Failed to create ticket. Please try again.');
                }
            }
        }
        
        $this->load->view('includes/header', $data);
        $this->load->view('ticket/create', $data);
        $this->load->view('includes/footer');
    }

    public function view($ticket_id) {
        $ticket = $this->Ticket_model->get_ticket($ticket_id, $this->session->userdata('user_id'));
        
        if (!$ticket) {
            show_404();
        }
        
        $data['title'] = 'Ticket #' . $ticket->ticket_number;
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        $data['ticket'] = $ticket;
        $data['messages'] = $this->Ticket_model->get_messages($ticket_id);
        
        // Handle reply submission
        if ($this->input->method() === 'post') {
            $this->form_validation->set_rules('message', 'Message', 'required|min_length[5]');
            
            if ($this->form_validation->run()) {
                $message_data = [
                    'ticket_id' => $ticket_id,
                    'user_id' => $this->session->userdata('user_id'),
                    'message' => $this->input->post('message'),
                    'is_internal' => 0
                ];
                
                if ($this->Ticket_model->add_message($message_data)) {
                    // Update ticket status to waiting for response if it was resolved
                    if ($ticket->status === 'resolved') {
                        $this->Ticket_model->update_ticket($ticket_id, ['status' => 'waiting_customer']);
                    }
                    
                    $this->session->set_flashdata('success', 'Reply added successfully!');
                    redirect('ticket/view/' . $ticket_id);
                } else {
                    $this->session->set_flashdata('error', 'Failed to add reply. Please try again.');
                }
            }
        }
        
        $this->load->view('includes/header', $data);
        $this->load->view('ticket/view', $data);
        $this->load->view('includes/footer');
    }

    public function close($ticket_id) {
        $ticket = $this->Ticket_model->get_ticket($ticket_id, $this->session->userdata('user_id'));
        
        if (!$ticket) {
            show_404();
        }
        
        if ($this->Ticket_model->close_ticket($ticket_id)) {
            $this->session->set_flashdata('success', 'Ticket closed successfully.');
        } else {
            $this->session->set_flashdata('error', 'Failed to close ticket.');
        }
        
        redirect('ticket/view/' . $ticket_id);
    }

    public function reopen($ticket_id) {
        $ticket = $this->Ticket_model->get_ticket($ticket_id, $this->session->userdata('user_id'));
        
        if (!$ticket) {
            show_404();
        }
        
        $update_data = [
            'status' => 'open',
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($this->Ticket_model->update_ticket($ticket_id, $update_data)) {
            $this->session->set_flashdata('success', 'Ticket reopened successfully.');
        } else {
            $this->session->set_flashdata('error', 'Failed to reopen ticket.');
        }
        
        redirect('ticket/view/' . $ticket_id);
    }

    public function search() {
        $data['title'] = 'Search Tickets';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        
        $search_query = $this->input->get('q');
        $status_filter = $this->input->get('status');
        $category_filter = $this->input->get('category');
        
        if ($search_query || $status_filter || $category_filter) {
            // Perform search (simplified - in production you'd want full-text search)
            $tickets = $this->Ticket_model->get_tickets($this->session->userdata('user_id'), $status_filter);
            
            // Filter by search query if provided
            if ($search_query) {
                $tickets = array_filter($tickets, function($ticket) use ($search_query) {
                    return stripos($ticket->subject, $search_query) !== false || 
                           stripos($ticket->ticket_number, $search_query) !== false;
                });
            }
            
            // Filter by category if provided
            if ($category_filter) {
                $tickets = array_filter($tickets, function($ticket) use ($category_filter) {
                    return $ticket->category === $category_filter;
                });
            }
            
            $data['tickets'] = $tickets;
            $data['search_performed'] = true;
        } else {
            $data['tickets'] = [];
            $data['search_performed'] = false;
        }
        
        $data['search_query'] = $search_query;
        $data['status_filter'] = $status_filter;
        $data['category_filter'] = $category_filter;
        
        $this->load->view('includes/header', $data);
        $this->load->view('ticket/search', $data);
        $this->load->view('includes/footer');
    }
} 