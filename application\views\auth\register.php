<?php
defined('BASEPATH') OR exit('No direct script access allowed');
// Load settings
if (!isset($GLOBALS['site_settings_loaded'])) {
    include_once VIEWPATH . 'includes/settings_loader.php';
}
?><!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Register - <?= site_name() ?></title>
	<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
	<link href="<?php echo secure_asset('assets/css/style.css'); ?>" rel="stylesheet">
	<link href="<?php echo secure_asset('assets/css/auth.css'); ?>" rel="stylesheet">
</head>
<body>
	<?php $this->load->view('includes/header'); ?>

	<!-- Auth Section -->
	<section class="auth-section">
		<div class="auth-bg"></div>
		<div class="auth-container">
			<div class="auth-card">
				<div class="auth-header">
					<div class="auth-logo">
						<img src="<?php echo base_url(); ?>assets/images/logo.png" alt="Dopminer">
					</div>
					<h1>Registrer</h1>
					<p>Buat akun baru untuk mulai hosting server gaming<br>kamu dengan harga terjangkau dan berkualitas</p>
				</div>

				<?php if (isset($error) && !empty($error)): ?>
					<div class="alert alert-danger">
						<?php echo $error; ?>
					</div>
				<?php endif; ?>

				<form class="auth-form" action="<?php echo base_url('auth/register'); ?>" method="POST">
					<div class="form-group">
						<label for="full_name">
							<i class="fas fa-user"></i>
							Nama Lengkap
						</label>
						<input type="text" id="full_name" name="full_name" required>
					</div>

					<div class="form-group">
						<label for="username">
							<i class="fas fa-user-circle"></i>
							Username
						</label>
						<input type="text" id="username" name="username" required minlength="4">
					</div>

					<div class="form-group">
						<label for="email">
							<i class="fas fa-envelope"></i>
							Email Address
						</label>
						<input type="email" id="email" name="email" required>
					</div>

					<div class="form-group">
						<label for="password">
							<i class="fas fa-lock"></i>
							Password
						</label>
						<div class="password-field">
							<input type="password" id="password" name="password" required minlength="8">
							<button type="button" class="password-toggle" onclick="togglePassword('password')">
								<i class="fas fa-eye"></i>
							</button>
						</div>
						<div class="password-strength">
							<div class="strength-bar"></div>
							<div class="strength-text">Password strength</div>
						</div>
					</div>

					<div class="form-group">
						<label for="confirm_password">
							<i class="fas fa-lock"></i>
							Konfirmasi Password
						</label>
						<div class="password-field">
							<input type="password" id="confirm_password" name="confirm_password" required>
							<button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">
								<i class="fas fa-eye"></i>
							</button>
						</div>
					</div>

					<div class="form-group">
						<div class="agreement-wrapper">
							<input type="checkbox" id="agree" name="agree" required>
							<label for="agree">Saya setuju dengan <a href="#" class="link">Terms of Service</a> dan <a href="#" class="link">Privacy Policy</a></label>
						</div>
					</div>


					<button type="submit" class="auth-btn">
						<i class="fas fa-user-plus"></i>
						Buat Akun Sekarang
					</button>
				</form>
<!-- 
				<div class="auth-divider">
					<span>atau daftar dengan</span>
				</div>

				<div class="social-login">
					<button class="social-btn google">
						<i class="fab fa-google"></i>
						Daftar dengan Google
					</button>
					<button class="social-btn discord">
						<i class="fab fa-discord"></i>
						Daftar dengan Discord
					</button>
				</div> -->

				<div class="auth-footer">
					<p>Sudah punya akun? <a href="<?php echo base_url('auth/login'); ?>">Masuk di sini</a></p>
				</div>
			</div>

			<div class="auth-info">
				<div class="info-card">
					<div class="info-icon">
						<i class="fas fa-rocket"></i>
					</div>
					<h3>Setup Instan</h3>
					<p>Server gaming siap dalam 60 detik dengan one-click installer untuk semua game populer.</p>
				</div>
				<div class="info-card">
					<div class="info-icon">
						<i class="fas fa-globe"></i>
					</div>
					<h3>Network Global</h3>
					<p>Data center strategis di Jakarta, Singapore, dan Amerika untuk ping ultra-rendah.</p>
				</div>
				<div class="info-card">
					<div class="info-icon">
						<i class="fas fa-chart-line"></i>
					</div>
					<h3>Auto Scaling</h3>
					<p>Resources yang menyesuaikan beban server secara otomatis untuk performa optimal.</p>
				</div>
			</div>
		</div>
	</section>

	<script>
		function togglePassword(fieldId) {
			const passwordField = document.getElementById(fieldId);
			const toggleBtn = passwordField.nextElementSibling.querySelector('i');
			
			if (passwordField.type === 'password') {
				passwordField.type = 'text';
				toggleBtn.className = 'fas fa-eye-slash';
			} else {
				passwordField.type = 'password';
				toggleBtn.className = 'fas fa-eye';
			}
		}

		// Password strength checker
		document.getElementById('password').addEventListener('input', function() {
			const password = this.value;
			const strengthBar = document.querySelector('.strength-bar');
			const strengthText = document.querySelector('.strength-text');
			
			let score = 0;
			if (password.length >= 8) score++;
			if (/[a-z]/.test(password)) score++;
			if (/[A-Z]/.test(password)) score++;
			if (/[0-9]/.test(password)) score++;
			if (/[^A-Za-z0-9]/.test(password)) score++;
			
			let strength = '';
			let color = '';
			
			switch(score) {
				case 0:
				case 1:
					strength = 'Lemah';
					color = '#ef4444';
					break;
				case 2:
				case 3:
					strength = 'Sedang';
					color = '#f59e0b';
					break;
				case 4:
					strength = 'Kuat';
					color = '#10b981';
					break;
				case 5:
					strength = 'Sangat Kuat';
					color = '#059669';
					break;
			}
			
			strengthBar.style.width = (score * 20) + '%';
			strengthBar.style.backgroundColor = color;
			strengthText.textContent = strength;
			strengthText.style.color = color;
		});

		// Confirm password validation
		document.getElementById('confirm_password').addEventListener('input', function() {
			const password = document.getElementById('password').value;
			const confirmPassword = this.value;
			
			if (confirmPassword !== '' && password !== confirmPassword) {
				this.style.borderColor = '#ef4444';
			} else {
				this.style.borderColor = '';
			}
		});

		// Add floating particles
		document.addEventListener('DOMContentLoaded', function() {
			createFloatingParticles();
		});

		function createFloatingParticles() {
			const authBg = document.querySelector('.auth-bg');
			const colors = ['#0EA5E9', '#A855F7', '#FF6B00'];
			
			for (let i = 0; i < 15; i++) {
				const particle = document.createElement('div');
				particle.className = 'floating-particle';
				particle.style.cssText = `
					position: absolute;
					width: ${Math.random() * 4 + 2}px;
					height: ${Math.random() * 4 + 2}px;
					background: ${colors[Math.floor(Math.random() * colors.length)]};
					border-radius: 50%;
					opacity: ${Math.random() * 0.3 + 0.1};
					animation: floatAuth ${Math.random() * 20 + 15}s linear infinite;
					left: ${Math.random() * 100}%;
					top: ${Math.random() * 100}%;
				`;
				authBg.appendChild(particle);
			}
		}
	</script>

	<style>
		@keyframes floatAuth {
			0% {
				transform: translateY(0) rotate(0deg);
				opacity: 0;
			}
			10% {
				opacity: 1;
			}
			90% {
				opacity: 1;
			}
			100% {
				transform: translateY(-50px) rotate(360deg);
				opacity: 0;
			}
		}
	</style>
</body>
</html> 