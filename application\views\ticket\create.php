<div class="ticket-create-container">
    <div class="container">
        <div class="page-header">
            <div class="header-nav">
                <a href="<?= base_url('ticket') ?>" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                    Back to Tickets
                </a>
            </div>
            <h1 class="page-title">Create Support Ticket</h1>
            <p class="page-subtitle">Get help from our support team</p>
        </div>

        <div class="ticket-form-container">
            <form method="post" action="<?= base_url('ticket/create') ?>" class="ticket-form" id="ticketForm">
                <div class="form-grid">
                    <!-- Ticket Information -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-info-circle"></i>
                            Ticket Information
                        </h3>
                        
                        <div class="form-group">
                            <label for="subject" class="form-label">Subject *</label>
                            <input type="text" 
                                   id="subject" 
                                   name="subject" 
                                   class="form-control" 
                                   placeholder="Brief description of your issue"
                                   value="<?= set_value('subject') ?>"
                                   maxlength="200"
                                   required>
                            <?= form_error('subject', '<div class="error-message">', '</div>') ?>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="priority" class="form-label">Priority *</label>
                                <select id="priority" name="priority" class="form-control" required>
                                    <option value="">Select Priority</option>
                                    <option value="low" <?= set_select('priority', 'low') ?>>Low</option>
                                    <option value="normal" <?= set_select('priority', 'normal', true) ?>>Normal</option>
                                    <option value="high" <?= set_select('priority', 'high') ?>>High</option>
                                    <option value="urgent" <?= set_select('priority', 'urgent') ?>>Urgent</option>
                                </select>
                                <?= form_error('priority', '<div class="error-message">', '</div>') ?>
                            </div>

                            <div class="form-group">
                                <label for="category" class="form-label">Category *</label>
                                <select id="category" name="category" class="form-control" required>
                                    <option value="">Select Category</option>
                                    <option value="general" <?= set_select('category', 'general') ?>>General Question</option>
                                    <option value="billing" <?= set_select('category', 'billing') ?>>Billing & Payment</option>
                                    <option value="technical" <?= set_select('category', 'technical') ?>>Technical Support</option>
                                    <option value="server_issue" <?= set_select('category', 'server_issue') ?>>Server Issue</option>
                                    <option value="abuse" <?= set_select('category', 'abuse') ?>>Report Abuse</option>
                                </select>
                                <?= form_error('category', '<div class="error-message">', '</div>') ?>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="order_id" class="form-label">Related Order (Optional)</label>
                            <select id="order_id" name="order_id" class="form-control">
                                <option value="">Select an order (if applicable)</option>
                                <?php foreach ($orders as $order): ?>
                                    <option value="<?= $order->id ?>" <?= set_select('order_id', $order->id) ?>>
                                        #<?= $order->order_number ?> - <?= $order->plan_name ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <!-- Message -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-comment"></i>
                            Message
                        </h3>
                        
                        <div class="form-group">
                            <label for="message" class="form-label">Describe your issue *</label>
                            <textarea id="message" 
                                      name="message" 
                                      class="form-control message-textarea" 
                                      placeholder="Please provide as much detail as possible about your issue..."
                                      rows="8"
                                      required><?= set_value('message') ?></textarea>
                            <?= form_error('message', '<div class="error-message">', '</div>') ?>
                            <div class="character-count">
                                <span id="charCount">0</span> characters
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Priority Information -->
                <div class="priority-info">
                    <h4 class="info-title">Priority Guidelines</h4>
                    <div class="priority-grid">
                        <div class="priority-item">
                            <div class="priority-badge low">Low</div>
                            <div class="priority-desc">General questions, feature requests</div>
                            <div class="priority-time">Response: 24-48 hours</div>
                        </div>
                        <div class="priority-item">
                            <div class="priority-badge normal">Normal</div>
                            <div class="priority-desc">Standard technical issues</div>
                            <div class="priority-time">Response: 12-24 hours</div>
                        </div>
                        <div class="priority-item">
                            <div class="priority-badge high">High</div>
                            <div class="priority-desc">Service affecting issues</div>
                            <div class="priority-time">Response: 4-12 hours</div>
                        </div>
                        <div class="priority-item">
                            <div class="priority-badge urgent">Urgent</div>
                            <div class="priority-desc">Critical service outages</div>
                            <div class="priority-time">Response: 1-4 hours</div>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <a href="<?= base_url('ticket') ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i>
                        Create Ticket
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.ticket-create-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 2rem 0;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 2rem;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.header-nav {
    margin-bottom: 2rem;
}

.back-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.back-btn:hover {
    color: #764ba2;
    transform: translateX(-4px);
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    font-size: 1.2rem;
    color: #718096;
}

.ticket-form-container {
    background: white;
    border-radius: 16px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.form-grid {
    display: grid;
    gap: 2rem;
    margin-bottom: 2rem;
}

.form-section {
    background: #f7fafc;
    border-radius: 12px;
    padding: 2rem;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #e2e8f0;
}

.section-title i {
    color: #667eea;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-label {
    display: block;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.message-textarea {
    resize: vertical;
    min-height: 150px;
}

.character-count {
    text-align: right;
    color: #718096;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.error-message {
    color: #e53e3e;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.priority-info {
    background: #f7fafc;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.info-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
}

.priority-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.priority-item {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
}

.priority-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
}

.priority-badge.low {
    background: #c6f6d5;
    color: #22543d;
}

.priority-badge.normal {
    background: #bee3f8;
    color: #1a365d;
}

.priority-badge.high {
    background: #fed7d7;
    color: #742a2a;
}

.priority-badge.urgent {
    background: #fbb6ce;
    color: #702459;
}

.priority-desc {
    font-size: 0.875rem;
    color: #4a5568;
    margin-bottom: 0.25rem;
}

.priority-time {
    font-size: 0.75rem;
    color: #718096;
    font-weight: 600;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 2rem;
    border-top: 2px solid #f7fafc;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
    transform: translateY(-2px);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .priority-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .page-title {
        font-size: 2rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counter for message textarea
    const messageTextarea = document.getElementById('message');
    const charCount = document.getElementById('charCount');
    
    function updateCharCount() {
        const count = messageTextarea.value.length;
        charCount.textContent = count;
        
        if (count > 1000) {
            charCount.style.color = '#e53e3e';
        } else if (count > 800) {
            charCount.style.color = '#d69e2e';
        } else {
            charCount.style.color = '#718096';
        }
    }
    
    messageTextarea.addEventListener('input', updateCharCount);
    updateCharCount(); // Initial count
    
    // Form validation
    document.getElementById('ticketForm').addEventListener('submit', function(e) {
        const subject = document.getElementById('subject').value.trim();
        const message = document.getElementById('message').value.trim();
        
        if (subject.length < 5) {
            e.preventDefault();
            alert('Subject must be at least 5 characters long.');
            document.getElementById('subject').focus();
            return;
        }
        
        if (message.length < 10) {
            e.preventDefault();
            alert('Message must be at least 10 characters long.');
            document.getElementById('message').focus();
            return;
        }
    });
    
    // Priority selection helper
    const prioritySelect = document.getElementById('priority');
    const categorySelect = document.getElementById('category');
    
    // Auto-suggest priority based on category
    categorySelect.addEventListener('change', function() {
        const category = this.value;
        let suggestedPriority = 'normal';
        
        switch (category) {
            case 'server_issue':
                suggestedPriority = 'high';
                break;
            case 'billing':
                suggestedPriority = 'normal';
                break;
            case 'abuse':
                suggestedPriority = 'urgent';
                break;
            case 'general':
                suggestedPriority = 'low';
                break;
        }
        
        if (prioritySelect.value === '' || prioritySelect.value === 'normal') {
            prioritySelect.value = suggestedPriority;
        }
    });
    
    // Add animation to form sections
    const formSections = document.querySelectorAll('.form-section, .priority-info');
    formSections.forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';
        section.style.transition = 'all 0.6s ease';
        
        setTimeout(() => {
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
        }, index * 200);
    });
});
</script> 