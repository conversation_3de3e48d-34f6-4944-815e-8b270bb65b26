<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Asset Controller - Secure Asset Delivery
 * Handles protected CSS and JS file delivery with token validation
 */
class Asset extends CI_Controller {

    private $secret_key = 'DopMiner_Secret_2024'; // Same as in helper
    private $allowed_extensions = ['css', 'js'];
    private $token_lifetime = 3600; // 1 hour
    
    public function __construct() {
        parent::__construct();
        $this->load->helper('asset');
    }
    
    /**
     * Serve protected CSS files
     */
    public function css($filename = null) {
        $this->serve_asset('css', $filename);
    }
    
    /**
     * Serve protected JS files
     */
    public function js($filename = null) {
        $this->serve_asset('js', $filename);
    }
    
    /**
     * Main asset serving function with security checks
     */
    private function serve_asset($type, $filename = null) {
        // Get parameters - filename can come from URL or GET parameter
        if (!$filename) {
            $filename = $this->input->get('file');
        }
        $timestamp = $this->input->get('t');
        $token = $this->input->get('token');
        
        // Basic validation
        if (!$filename || !$timestamp || !$token) {
            $this->send_403('Missing parameters');
            return;
        }
        
        // Check file extension
        $ext = pathinfo($filename, PATHINFO_EXTENSION);
        if (!in_array($ext, $this->allowed_extensions)) {
            $this->send_403('Invalid file type');
            return;
        }
        
        // Check timestamp (prevent replay attacks)
        if (time() - $timestamp > $this->token_lifetime) {
            $this->send_403('Token expired');
            return;
        }
        
        // Validate token
        $asset_path = 'assets/' . $type . '/' . $filename;
        $expected_token = md5($asset_path . $timestamp . $this->secret_key);
        
        if (substr($expected_token, 0, 16) !== $token) {
            $this->send_403('Invalid token');
            return;
        }
        
        // Check referer (additional security)
        $referer = $this->input->server('HTTP_REFERER');
        $base_url = $this->config->item('base_url');
        
        if (!$referer || strpos($referer, $base_url) !== 0) {
            // Allow localhost for development
            $allowed_hosts = ['localhost', '127.0.0.1'];
            $referer_host = parse_url($referer, PHP_URL_HOST);
            
            if (!in_array($referer_host, $allowed_hosts)) {
                $this->send_403('Invalid referer');
                return;
            }
        }
        
        // Build file path
        $file_path = FCPATH . $asset_path;
        
        // Check if file exists and is readable
        if (!file_exists($file_path) || !is_readable($file_path)) {
            $this->send_404('File not found');
            return;
        }
        
        // Serve the file
        $this->serve_file($file_path, $type);
    }
    
    /**
     * Serve the actual file with proper headers
     */
    private function serve_file($file_path, $type) {
        $content = file_get_contents($file_path);
        
        // Obfuscate content for additional security
        if ($type === 'css') {
            $content = obfuscate_css($content);
            $mime_type = 'text/css';
        } else {
            $content = obfuscate_js($content);
            $mime_type = 'application/javascript';
        }
        
        // Set security headers
        header('Content-Type: ' . $mime_type);
        header('Cache-Control: public, max-age=3600'); // Cache for 1 hour
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // Add ETag for caching
        $etag = md5($content);
        header('ETag: "' . $etag . '"');
        
        // Check if client has cached version
        $client_etag = $this->input->server('HTTP_IF_NONE_MATCH');
        if ($client_etag && $client_etag === '"' . $etag . '"') {
            header('HTTP/1.1 304 Not Modified');
            return;
        }
        
        // Output content
        echo $content;
    }
    
    /**
     * Send 403 Forbidden response
     */
    private function send_403($reason = 'Access denied') {
        header('HTTP/1.1 403 Forbidden');
        header('Content-Type: text/html; charset=UTF-8');
        
        $blocked_content = file_get_contents(FCPATH . 'assets/blocked.html');
        echo $blocked_content;
        
        // Log security attempt
        log_message('security', 'Asset access denied: ' . $reason . ' | IP: ' . $this->input->ip_address() . ' | User Agent: ' . $this->input->user_agent());
    }
    
    /**
     * Send 404 Not Found response
     */
    private function send_404($reason = 'File not found') {
        header('HTTP/1.1 404 Not Found');
        header('Content-Type: text/html; charset=UTF-8');
        
        echo '<h1>404 - File Not Found</h1>';
        echo '<p>The requested asset could not be found.</p>';
        
        log_message('error', 'Asset not found: ' . $reason);
    }
} 