<?php
defined('BASEPATH') OR exit('No direct script access allowed');
$data['title'] = $title ?? 'Add Server Plan';

$content = '<div class="page-header">
    <h1>Add New Server Plan</h1>
    <p>Create a new server hosting plan</p>
</div>';

// Show validation errors
if (validation_errors()) {
    $content .= '<div class="alert alert-danger">' . validation_errors() . '</div>';
}

$content .= '<form method="post" action="' . base_url('admin/add_plan') . '">
    <div class="admin-card">
        <div class="card-header">
            <h2><i class="fas fa-server"></i> Plan Information</h2>
        </div>
        <div style="padding: 2rem;">
            <div class="form-grid">
                <div class="form-group">
                    <label for="category_id">Category *</label>
                    <select name="category_id" id="category_id" class="form-control" required>';

if (isset($categories) && !empty($categories)) {
    foreach ($categories as $category) {
        $content .= '<option value="' . $category->id . '" ' . set_select('category_id', $category->id) . '>' . $category->name . '</option>';
    }
}

$content .= '</select>
                </div>
                
                <div class="form-group">
                    <label for="name">Plan Name *</label>
                    <input type="text" name="name" id="name" class="form-control" 
                           value="' . set_value('name') . '" required>
                </div>
                
                <div class="form-group">
                    <label for="slug">Slug *</label>
                    <input type="text" name="slug" id="slug" class="form-control" 
                           value="' . set_value('slug') . '" required>
                    <small class="form-text">URL-friendly name (e.g., fivem-starter)</small>
                </div>
                
                <div class="form-group full-width">
                    <label for="description">Description</label>
                    <textarea name="description" id="description" class="form-control" rows="3">' . set_value('description') . '</textarea>
                </div>
            </div>
        </div>
    </div>
    
    <div class="admin-card">
        <div class="card-header">
            <h2><i class="fas fa-cog"></i> Specifications</h2>
        </div>
        <div style="padding: 2rem;">
            <div class="form-grid">
                <div class="form-group">
                    <label for="cpu">CPU</label>
                    <input type="text" name="cpu" id="cpu" class="form-control" 
                           value="' . set_value('cpu') . '" placeholder="e.g., 2 vCPU">
                </div>
                
                <div class="form-group">
                    <label for="ram">RAM</label>
                    <input type="text" name="ram" id="ram" class="form-control" 
                           value="' . set_value('ram') . '" placeholder="e.g., 4 GB">
                </div>
                
                <div class="form-group">
                    <label for="storage">Storage</label>
                    <input type="text" name="storage" id="storage" class="form-control" 
                           value="' . set_value('storage') . '" placeholder="e.g., 50 GB SSD">
                </div>
                
                <div class="form-group">
                    <label for="bandwidth">Bandwidth</label>
                    <input type="text" name="bandwidth" id="bandwidth" class="form-control" 
                           value="' . set_value('bandwidth') . '" placeholder="e.g., Unlimited">
                </div>
                
                <div class="form-group">
                    <label for="max_slots">Max Slots</label>
                    <input type="number" name="max_slots" id="max_slots" class="form-control" 
                           value="' . set_value('max_slots') . '" min="1">
                </div>
            </div>
        </div>
    </div>
    
    <div class="admin-card">
        <div class="card-header">
            <h2><i class="fas fa-dollar-sign"></i> Pricing</h2>
        </div>
        <div style="padding: 2rem;">
            <div class="form-grid">
                <div class="form-group">
                    <label for="price_monthly">Monthly Price (IDR) *</label>
                    <input type="number" name="price_monthly" id="price_monthly" class="form-control" 
                           value="' . set_value('price_monthly') . '" min="0" step="1000" required>
                </div>
                
                <div class="form-group">
                    <label for="price_quarterly">Quarterly Price (IDR)</label>
                    <input type="number" name="price_quarterly" id="price_quarterly" class="form-control" 
                           value="' . set_value('price_quarterly') . '" min="0" step="1000">
                </div>
                
                <div class="form-group">
                    <label for="price_yearly">Yearly Price (IDR)</label>
                    <input type="number" name="price_yearly" id="price_yearly" class="form-control" 
                           value="' . set_value('price_yearly') . '" min="0" step="1000">
                </div>
                
                <div class="form-group">
                    <label for="setup_fee">Setup Fee (IDR)</label>
                    <input type="number" name="setup_fee" id="setup_fee" class="form-control" 
                           value="' . set_value('setup_fee', '0') . '" min="0" step="1000">
                </div>
                
                <div class="form-group">
                    <label for="sort_order">Sort Order</label>
                    <input type="number" name="sort_order" id="sort_order" class="form-control" 
                           value="' . set_value('sort_order', '0') . '" min="0">
                </div>
            </div>
        </div>
    </div>
    
    <div class="admin-card">
        <div class="card-header">
            <h2><i class="fas fa-list"></i> Features</h2>
        </div>
        <div style="padding: 2rem;">
            <div class="form-group">
                <label for="features">Features (one per line)</label>
                <textarea name="features" id="features" class="form-control" rows="6" 
                          placeholder="DDoS Protection&#10;Daily Backups&#10;24/7 Support&#10;Free Domain">' . set_value('features') . '</textarea>
                <small class="form-text">Enter each feature on a new line</small>
            </div>
        </div>
    </div>
    
    <div class="form-actions">
        <a href="' . base_url('admin/server_plans') . '" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Plans
        </a>
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-save"></i> Create Plan
        </button>
    </div>
</form>';

$content .= '<style>
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}
.form-group.full-width {
    grid-column: 1 / -1;
}
.form-actions {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}
.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
}
.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
}

/* Auto-generate slug from name */
document.addEventListener("DOMContentLoaded", function() {
    const nameInput = document.getElementById("name");
    const slugInput = document.getElementById("slug");
    
    nameInput.addEventListener("input", function() {
        const slug = this.value.toLowerCase()
            .replace(/[^a-z0-9\s-]/g, "")
            .replace(/\s+/g, "-")
            .replace(/-+/g, "-")
            .trim("-");
        slugInput.value = slug;
    });
});
</style>

<script>
document.addEventListener("DOMContentLoaded", function() {
    const nameInput = document.getElementById("name");
    const slugInput = document.getElementById("slug");
    
    nameInput.addEventListener("input", function() {
        const slug = this.value.toLowerCase()
            .replace(/[^a-z0-9\s-]/g, "")
            .replace(/\s+/g, "-")
            .replace(/-+/g, "-")
            .trim("-");
        slugInput.value = slug;
    });
});
</script>';

$data['content'] = $content;
$this->load->view('admin/admin_template', $data);
?> 