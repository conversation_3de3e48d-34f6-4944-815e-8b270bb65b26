<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Server_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    // ========== SERVER CATEGORIES ==========
    public function get_categories($type = null) {
        $this->db->where('status', 'active');
        if ($type) {
            $this->db->where('type', $type);
        }
        $this->db->order_by('sort_order', 'ASC');
        return $this->db->get('server_categories')->result();
    }

    public function get_category($id) {
        return $this->db->get_where('server_categories', ['id' => $id])->row();
    }

    public function get_category_by_slug($slug) {
        return $this->db->get_where('server_categories', ['slug' => $slug, 'status' => 'active'])->row();
    }

    // ========== SERVER PLANS ==========
    public function get_plans($category_id = null) {
        $this->db->select('sp.*, sc.name as category_name, sc.type as category_type');
        $this->db->from('server_plans sp');
        $this->db->join('server_categories sc', 'sp.category_id = sc.id');
        $this->db->where('sp.status', 'active');
        $this->db->where('sc.status', 'active');
        
        if ($category_id) {
            $this->db->where('sp.category_id', $category_id);
        }
        
        $this->db->order_by('sp.sort_order', 'ASC');
        $plans = $this->db->get()->result();
        
        // Decode JSON fields
        foreach ($plans as $plan) {
            $plan->specifications = json_decode($plan->specifications, true);
            $plan->features = json_decode($plan->features, true);
        }
        
        return $plans;
    }

    // Get all plans for admin (including inactive)
    public function get_all_plans($category_id = null) {
        $this->db->select('sp.*, sc.name as category_name, sc.type as category_type');
        $this->db->from('server_plans sp');
        $this->db->join('server_categories sc', 'sp.category_id = sc.id', 'left');
        
        if ($category_id) {
            $this->db->where('sp.category_id', $category_id);
        }
        
        $this->db->order_by('sp.sort_order', 'ASC');
        $plans = $this->db->get()->result();
        
        return $plans;
    }

    public function get_plan($plan_id) {
        $this->db->select('sp.*, sc.name as category_name, sc.type as category_type');
        $this->db->from('server_plans sp');
        $this->db->join('server_categories sc', 'sp.category_id = sc.id', 'left');
        $this->db->where('sp.id', $plan_id);
        $plan = $this->db->get()->row();
        
        return $plan;
    }

    public function get_plan_with_decoded_data($plan_id) {
        $plan = $this->get_plan($plan_id);
        
        if ($plan && isset($plan->specifications)) {
            $plan->specifications = json_decode($plan->specifications, true);
        }
        if ($plan && isset($plan->features)) {
            $plan->features = json_decode($plan->features, true);
        }
        
        return $plan;
    }

    public function get_plan_by_slug($slug) {
        $this->db->select('sp.*, sc.name as category_name, sc.type as category_type');
        $this->db->from('server_plans sp');
        $this->db->join('server_categories sc', 'sp.category_id = sc.id');
        $this->db->where('sp.slug', $slug);
        $this->db->where('sp.status', 'active');
        $this->db->where('sc.status', 'active');
        $plan = $this->db->get()->row();
        
        if ($plan) {
            $plan->specifications = json_decode($plan->specifications, true);
            $plan->features = json_decode($plan->features, true);
        }
        
        return $plan;
    }

    // ========== ORDERS ==========
    public function create_order($data) {
        // Generate unique order number
        $data['order_number'] = $this->generate_order_number();
        
        $this->db->insert('orders', $data);
        return $this->db->insert_id();
    }

    public function get_orders($user_id = null, $limit = null, $offset = null) {
        $this->db->select('o.*, sp.name as plan_name, sc.name as category_name, sc.type as category_type');
        $this->db->from('orders o');
        $this->db->join('server_plans sp', 'o.plan_id = sp.id');
        $this->db->join('server_categories sc', 'sp.category_id = sc.id');
        
        if ($user_id) {
            $this->db->where('o.user_id', $user_id);
        }
        
        $this->db->order_by('o.created_at', 'DESC');
        
        if ($limit) {
            $this->db->limit($limit, $offset);
        }
        
        return $this->db->get()->result();
    }

    public function get_order($id, $user_id = null) {
        $this->db->select('o.*, sp.name as plan_name, sp.specifications, sp.features, sc.name as category_name, sc.type as category_type, u.username, u.email');
        $this->db->from('orders o');
        $this->db->join('server_plans sp', 'o.plan_id = sp.id');
        $this->db->join('server_categories sc', 'sp.category_id = sc.id');
        $this->db->join('users u', 'o.user_id = u.id');
        $this->db->where('o.id', $id);
        
        if ($user_id) {
            $this->db->where('o.user_id', $user_id);
        }
        
        $order = $this->db->get()->row();
        
        if ($order) {
            $order->specifications = json_decode($order->specifications, true);
            $order->features = json_decode($order->features, true);
        }
        
        return $order;
    }

    public function get_order_by_number($order_number, $user_id = null) {
        $this->db->select('o.*, sp.name as plan_name, sc.name as category_name');
        $this->db->from('orders o');
        $this->db->join('server_plans sp', 'o.plan_id = sp.id');
        $this->db->join('server_categories sc', 'sp.category_id = sc.id');
        $this->db->where('o.order_number', $order_number);
        
        if ($user_id) {
            $this->db->where('o.user_id', $user_id);
        }
        
        return $this->db->get()->row();
    }

    public function update_order($id, $data) {
        $this->db->where('id', $id);
        return $this->db->update('orders', $data);
    }

    // ========== SERVERS ==========
    public function create_server($data) {
        $this->db->insert('servers', $data);
        return $this->db->insert_id();
    }

    public function get_servers($user_id = null) {
        $this->db->select('s.*, o.order_number, sp.name as plan_name, sc.name as category_name, sc.slug as category_slug, sc.type as category_type');
        $this->db->from('servers s');
        $this->db->join('orders o', 's.order_id = o.id');
        $this->db->join('server_plans sp', 's.plan_id = sp.id');
        $this->db->join('server_categories sc', 'sp.category_id = sc.id');
        
        if ($user_id) {
            $this->db->where('s.user_id', $user_id);
        }
        
        $this->db->order_by('s.created_at', 'DESC');
        $servers = $this->db->get()->result();
        
        // Decode JSON fields
        foreach ($servers as $server) {
            if ($server->game_config) {
                $server->game_config = json_decode($server->game_config, true);
            }
        }
        
        return $servers;
    }

    public function get_server($id, $user_id = null) {
        $this->db->select('s.*, o.order_number, o.billing_cycle, o.expires_at, sp.name as plan_name, sp.specifications, sp.features, sc.name as category_name, sc.type as category_type');
        $this->db->from('servers s');
        $this->db->join('orders o', 's.order_id = o.id');
        $this->db->join('server_plans sp', 's.plan_id = sp.id');
        $this->db->join('server_categories sc', 'sp.category_id = sc.id');
        $this->db->where('s.id', $id);
        
        if ($user_id) {
            $this->db->where('s.user_id', $user_id);
        }
        
        $server = $this->db->get()->row();
        
        if ($server) {
            if ($server->game_config) {
                $server->game_config = json_decode($server->game_config, true);
            }
            $server->specifications = json_decode($server->specifications, true);
            $server->features = json_decode($server->features, true);
        }
        
        return $server;
    }

    public function update_server($id, $data) {
        $this->db->where('id', $id);
        return $this->db->update('servers', $data);
    }

    // ========== HELPER FUNCTIONS ==========
    private function generate_order_number() {
        $prefix = 'ORD';
        $date = date('Ymd');
        
        // Get last order number for today
        $this->db->like('order_number', $prefix . $date, 'after');
        $this->db->order_by('id', 'DESC');
        $this->db->limit(1);
        $last_order = $this->db->get('orders')->row();
        
        if ($last_order) {
            $last_number = (int) substr($last_order->order_number, -4);
            $new_number = $last_number + 1;
        } else {
            $new_number = 1;
        }
        
        return $prefix . $date . str_pad($new_number, 4, '0', STR_PAD_LEFT);
    }

    public function get_price_for_cycle($plan, $cycle) {
        switch ($cycle) {
            case 'quarterly':
                return $plan->price_quarterly ?: ($plan->price_monthly * 3);
            case 'yearly':
                return $plan->price_yearly ?: ($plan->price_monthly * 12);
            default:
                return $plan->price_monthly;
        }
    }

    public function calculate_expiry_date($cycle) {
        switch ($cycle) {
            case 'quarterly':
                return date('Y-m-d H:i:s', strtotime('+3 months'));
            case 'yearly':
                return date('Y-m-d H:i:s', strtotime('+1 year'));
            default:
                return date('Y-m-d H:i:s', strtotime('+1 month'));
        }
    }

    // ========== STATISTICS ==========
    public function get_order_stats($user_id = null) {
        $this->db->select('
            COUNT(*) as total_orders,
            SUM(CASE WHEN payment_status = "paid" THEN total_amount ELSE 0 END) as total_revenue,
            COUNT(CASE WHEN payment_status = "pending" THEN 1 END) as pending_orders,
            COUNT(CASE WHEN server_status = "active" THEN 1 END) as active_servers
        ');
        
        if ($user_id) {
            $this->db->where('user_id', $user_id);
        }
        
        return $this->db->get('orders')->row();
    }

    public function get_popular_plans($limit = 5) {
        $this->db->select('sp.name, sc.name as category_name, COUNT(o.id) as order_count');
        $this->db->from('orders o');
        $this->db->join('server_plans sp', 'o.plan_id = sp.id');
        $this->db->join('server_categories sc', 'sp.category_id = sc.id');
        $this->db->where('o.payment_status', 'paid');
        $this->db->group_by('o.plan_id');
        $this->db->order_by('order_count', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get()->result();
    }
} 