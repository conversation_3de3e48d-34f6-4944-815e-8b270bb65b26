<?php
defined('BASEPATH') OR exit('No direct script access allowed');
$data['title'] = $title ?? 'Settings';

// Fields to hide from the settings page
$hidden_fields = [
    'payment_gateway',
    'midtrans_server_key', 
    'midtrans_client_key',
    'xendit_api_key',
    'smtp_crypto',
    'telegram_bot_token',
    'telegram_chat_id'
];

// Start building content
$content = '<div class="page-header">
    <h1>System Settings</h1>
    <p>Configure your website settings</p>
</div>

<form method="post" action="' . base_url('admin/settings') . '">
    <div class="admin-card">
        <div class="card-header">
            <h2><i class="fas fa-cog"></i> Website Settings</h2>
        </div>
        <div style="padding: 2rem;">
            <div class="settings-grid">';

if (isset($settings) && !empty($settings)):
    foreach ($settings as $setting):
        // Skip hidden fields
        if (in_array($setting->key, $hidden_fields)) {
            continue;
        }
        $content .= '<div class="form-group">
            <label for="' . $setting->key . '">' . ucwords(str_replace('_', ' ', $setting->key)) . '</label>';
        
        if ($setting->key == 'maintenance_mode' || $setting->key == 'registration_enabled'):
            $content .= '<select name="' . $setting->key . '" class="form-control">
                <option value="0" ' . ($setting->value == '0' ? 'selected' : '') . '>Disabled</option>
                <option value="1" ' . ($setting->value == '1' ? 'selected' : '') . '>Enabled</option>
            </select>';
        elseif (strpos($setting->key, 'pass') !== false || strpos($setting->key, 'key') !== false):
            $content .= '<input type="password" name="' . $setting->key . '" value="' . htmlspecialchars($setting->value ?? '') . '" class="form-control">';
        else:
            $content .= '<input type="text" name="' . $setting->key . '" value="' . htmlspecialchars($setting->value ?? '') . '" class="form-control">';
        endif;
        
        if ($setting->description):
            $content .= '<small class="form-text">' . $setting->description . '</small>';
        endif;
        
        $content .= '</div>';
    endforeach;
endif;

$content .= '</div>
        </div>
    </div>
    
    <div class="form-actions">
        <button type="submit" name="submit" class="btn btn-primary">
            <i class="fas fa-save"></i> Save Settings
        </button>
    </div>
</form>';

// Add styles
$content .= '<style>
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.form-actions {
    margin-top: 2rem;
    text-align: right;
}
</style>';

$data['content'] = $content;
$this->load->view('admin/admin_template', $data);
?> 