<?php
// Test untuk memeriksa apakah mod_rewrite aktif
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "<h2 style='color: green;'>✓ mod_rewrite AKTIF</h2>";
    } else {
        echo "<h2 style='color: red;'>✗ mod_rewrite TIDAK AKTIF</h2>";
    }
    echo "<p>Modules yang aktif:</p>";
    echo "<pre>";
    print_r($modules);
    echo "</pre>";
} else {
    echo "<h2 style='color: orange;'>Tidak dapat memeriksa mod_rewrite</h2>";
    echo "<p>Fungsi apache_get_modules() tidak tersedia</p>";
}

// Test .htaccess
echo "<hr>";
echo "<h3>Test .htaccess</h3>";
if (file_exists('.htaccess')) {
    echo "<p style='color: green;'>✓ File .htaccess ditemukan</p>";
    echo "<pre>";
    echo htmlspecialchars(file_get_contents('.htaccess'));
    echo "</pre>";
} else {
    echo "<p style='color: red;'>✗ File .htaccess tidak ditemukan</p>";
}

// Test file-file penting
echo "<hr>";
echo "<h3>Test File Controller</h3>";
$files = [
    'application/controllers/Auth.php',
    'application/controllers/Welcome.php',
    'application/views/auth/login.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✓ $file ada</p>";
    } else {
        echo "<p style='color: red;'>✗ $file tidak ada</p>";
    }
}

// Test akses langsung
echo "<hr>";
echo "<h3>Test Link</h3>";
echo "<p><a href='auth/login'>Test: auth/login</a></p>";
echo "<p><a href='index.php/auth/login'>Test: index.php/auth/login</a></p>";
echo "<p><a href='welcome'>Test: welcome</a></p>";