<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class User_model extends CI_Model {
    
    public function __construct() {
        parent::__construct();
        $this->load->database();
    }
    
    public function register($data) {
        // Hash password
        $data['password'] = password_hash($data['password'], PASSWORD_BCRYPT);
        
        // Insert user
        if ($this->db->insert('users', $data)) {
            return $this->db->insert_id();
        }
        return false;
    }
    
    public function login($email, $password) {
        // Get user by email
        $user = $this->db->get_where('users', ['email' => $email])->row();
        
        if ($user && password_verify($password, $user->password)) {
            // Update last login
            $this->db->where('id', $user->id);
            $this->db->update('users', ['last_login' => date('Y-m-d H:i:s')]);
            
            return $user;
        }
        
        return false;
    }
    
    public function get_user($user_id) {
        return $this->db->get_where('users', ['id' => $user_id])->row();
    }
    
    public function get_user_by_id($user_id) {
        return $this->get_user($user_id);
    }
    
    public function get_user_servers($user_id) {
        return $this->db->get_where('servers', ['user_id' => $user_id])->result();
    }
    
    public function get_user_transactions($user_id, $limit = null) {
        $this->db->where('user_id', $user_id);
        $this->db->order_by('created_at', 'DESC');
        
        if ($limit) {
            $this->db->limit($limit);
        }
        
        return $this->db->get('transactions')->result();
    }
    
    public function get_user_tickets($user_id, $limit = null) {
        $this->db->where('user_id', $user_id);
        $this->db->order_by('created_at', 'DESC');
        
        if ($limit) {
            $this->db->limit($limit);
        }
        
        return $this->db->get('support_tickets')->result();
    }
    
    public function update_profile($user_id, $data) {
        $this->db->where('id', $user_id);
        return $this->db->update('users', $data);
    }
    
    public function change_password($user_id, $new_password) {
        $this->db->where('id', $user_id);
        return $this->db->update('users', [
            'password' => password_hash($new_password, PASSWORD_BCRYPT)
        ]);
    }

    public function count_total_users() {
        return $this->db->count_all('users');
    }

    public function get_all_users($limit = null, $offset = null) {
        if ($limit) {
            $this->db->limit($limit, $offset);
        }
        $this->db->order_by('created_at', 'DESC');
        return $this->db->get('users')->result();
    }

    public function create_user($data) {
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_BCRYPT);
        }
        return $this->db->insert('users', $data);
    }

    public function update_user($user_id, $data) {
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_BCRYPT);
        } else {
            unset($data['password']);
        }
        
        $this->db->where('id', $user_id);
        return $this->db->update('users', $data);
    }

    public function delete_user($user_id) {
        // Don't allow deletion of admin users
        $user = $this->get_user($user_id);
        if ($user && $user->role === 'admin') {
            return false;
        }
        
        $this->db->where('id', $user_id);
        return $this->db->delete('users');
    }

    public function create_transaction($data) {
        return $this->db->insert('transactions', $data);
    }

    public function update_balance($user_id, $new_balance) {
        $this->db->where('id', $user_id);
        return $this->db->update('users', ['balance' => $new_balance]);
    }

    public function get_transaction_stats($user_id) {
        $this->db->select('
            COUNT(*) as total_transactions,
            SUM(CASE WHEN type = "topup" AND status = "success" THEN amount ELSE 0 END) as total_topup,
            SUM(CASE WHEN type = "purchase" AND status = "success" THEN amount ELSE 0 END) as total_spent,
            COUNT(CASE WHEN status = "pending" THEN 1 END) as pending_transactions
        ');
        $this->db->where('user_id', $user_id);
        return $this->db->get('transactions')->row();
    }
} 