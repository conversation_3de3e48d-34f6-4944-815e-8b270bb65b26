<div class="order-browse-container">
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="hero-content">
            <h1 class="hero-title">Choose Your Perfect Server</h1>
            <p class="hero-subtitle">Professional game server and VPS hosting solutions</p>
            <div class="hero-stats">
                <div class="stat-item">
                    <span class="stat-number">99.9%</span>
                    <span class="stat-label">Uptime</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">24/7</span>
                    <span class="stat-label">Support</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">1000+</span>
                    <span class="stat-label">Happy Clients</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Server Categories -->
    <div class="categories-section">
        <div class="container">
            <h2 class="section-title">Game Servers</h2>
            <div class="categories-grid">
                <?php foreach ($game_categories as $category): ?>
                <div class="category-card" data-category="<?= $category->slug ?>">
                    <div class="category-icon">
                        <img src="<?= base_url('assets/images/' . $category->icon) ?>" alt="<?= $category->name ?>" onerror="this.src='<?= base_url('assets/images/default-game.png') ?>'">
                    </div>
                    <div class="category-info">
                        <h3 class="category-name"><?= $category->name ?></h3>
                        <p class="category-description"><?= $category->description ?></p>
                        <a href="<?= base_url('order/category/' . $category->slug) ?>" class="category-btn">
                            <span>Browse Plans</span>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <h2 class="section-title">VPS Servers</h2>
            <div class="categories-grid">
                <?php foreach ($vps_categories as $category): ?>
                <div class="category-card" data-category="<?= $category->slug ?>">
                    <div class="category-icon">
                        <img src="<?= base_url('assets/images/' . $category->icon) ?>" alt="<?= $category->name ?>" onerror="this.src='<?= base_url('assets/images/default-vps.png') ?>'">
                    </div>
                    <div class="category-info">
                        <h3 class="category-name"><?= $category->name ?></h3>
                        <p class="category-description"><?= $category->description ?></p>
                        <a href="<?= base_url('order/category/' . $category->slug) ?>" class="category-btn">
                            <span>Browse Plans</span>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- Featured Plans -->
    <div class="featured-section">
        <div class="container">
            <h2 class="section-title">Featured Server Plans</h2>
            <div class="plans-grid">
                <?php foreach (array_slice($featured_plans, 0, 6) as $plan): ?>
                <div class="plan-card">
                    <div class="plan-header">
                        <div class="plan-category"><?= $plan->category_name ?></div>
                        <h3 class="plan-name"><?= $plan->name ?></h3>
                        <div class="plan-price">
                            <span class="price-amount">Rp <?= number_format($plan->price_monthly, 0, ',', '.') ?></span>
                            <span class="price-period">/month</span>
                        </div>
                    </div>
                    
                    <div class="plan-specs">
                        <?php if ($plan->specifications): ?>
                            <div class="spec-item">
                                <i class="fas fa-microchip"></i>
                                <span><?= $plan->specifications['cpu'] ?></span>
                            </div>
                            <div class="spec-item">
                                <i class="fas fa-memory"></i>
                                <span><?= $plan->specifications['ram'] ?></span>
                            </div>
                            <div class="spec-item">
                                <i class="fas fa-hdd"></i>
                                <span><?= $plan->specifications['storage'] ?></span>
                            </div>
                            <div class="spec-item">
                                <i class="fas fa-network-wired"></i>
                                <span><?= $plan->specifications['bandwidth'] ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($plan->max_slots): ?>
                            <div class="spec-item">
                                <i class="fas fa-users"></i>
                                <span>Up to <?= $plan->max_slots ?> players</span>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="plan-features">
                        <?php if ($plan->features): ?>
                            <?php foreach (array_slice($plan->features, 0, 3) as $feature): ?>
                                <div class="feature-item">
                                    <i class="fas fa-check"></i>
                                    <span><?= $feature ?></span>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>

                    <div class="plan-actions">
                        <a href="<?= base_url('order/configure/' . $plan->slug) ?>" class="btn btn-primary">
                            <i class="fas fa-shopping-cart"></i>
                            Order Now
                        </a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- Why Choose Us -->
    <div class="features-section">
        <div class="container">
            <h2 class="section-title">Why Choose Dopminer?</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">DDoS Protection</h3>
                    <p class="feature-description">Advanced DDoS protection keeps your server online 24/7</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3 class="feature-title">24/7 Support</h3>
                    <p class="feature-description">Expert support team available around the clock</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h3 class="feature-title">Instant Setup</h3>
                    <p class="feature-description">Your server is ready within minutes of payment</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3 class="feature-title">Auto Backups</h3>
                    <p class="feature-description">Daily automatic backups to keep your data safe</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.order-browse-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding-top: 2rem;
}

.hero-section {
    text-align: center;
    padding: 4rem 2rem;
    color: white;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #fff, #f0f8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    opacity: 0.9;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 4rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffd700;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.8;
}

.categories-section, .featured-section, .features-section {
    background: white;
    padding: 4rem 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #2d3748;
}

.categories-grid, .plans-grid, .features-grid {
    display: grid;
    gap: 2rem;
    margin-bottom: 4rem;
}

.categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.plans-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.features-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.category-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.category-icon {
    text-align: center;
    margin-bottom: 1.5rem;
}

.category-icon img {
    width: 64px;
    height: 64px;
    object-fit: contain;
}

.category-name {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2d3748;
}

.category-description {
    color: #718096;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.category-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.category-btn:hover {
    transform: translateX(4px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.plan-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.plan-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.plan-header {
    text-align: center;
    margin-bottom: 2rem;
}

.plan-category {
    background: #667eea;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 1rem;
}

.plan-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #2d3748;
}

.plan-price {
    margin-bottom: 1rem;
}

.price-amount {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
}

.price-period {
    color: #718096;
    font-size: 1rem;
}

.plan-specs, .plan-features {
    margin-bottom: 2rem;
}

.spec-item, .feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: #4a5568;
}

.spec-item i, .feature-item i {
    color: #667eea;
    width: 16px;
}

.plan-actions {
    text-align: center;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.feature-card {
    text-align: center;
    padding: 2rem;
    border-radius: 16px;
    background: #f7fafc;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-4px);
    background: white;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2d3748;
}

.feature-description {
    color: #718096;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 2rem;
    }
    
    .categories-grid, .plans-grid, .features-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
// Add smooth scrolling and animations
document.addEventListener('DOMContentLoaded', function() {
    // Animate cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe all cards
    document.querySelectorAll('.category-card, .plan-card, .feature-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s ease';
        observer.observe(card);
    });
    
    // Add click handlers for category cards
    document.querySelectorAll('.category-card').forEach(card => {
        card.addEventListener('click', function() {
            const categorySlug = this.dataset.category;
            window.location.href = '<?= base_url("order/category/") ?>' + categorySlug;
        });
    });
});
</script> 