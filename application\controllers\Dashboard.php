<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('User_model');
        $this->load->model('Server_model');
        $this->load->model('Ticket_model');
        $this->load->library('session');
        $this->load->helper('url');
        
        // Check if user is logged in
        if (!$this->session->userdata('user_id')) {
            redirect('auth/login');
        }
    }

    public function index() {
        $user_id = $this->session->userdata('user_id');
        
        $data['user'] = $this->User_model->get_user($user_id);
        $data['servers'] = $this->Server_model->get_servers($user_id);
        $data['orders'] = $this->Server_model->get_orders($user_id, 5);
        $data['tickets'] = $this->Ticket_model->get_tickets($user_id, null, 5);
        $data['transactions'] = $this->User_model->get_user_transactions($user_id, 5);
        
        // Get statistics
        $data['order_stats'] = $this->Server_model->get_order_stats($user_id);
        $data['ticket_stats'] = $this->Ticket_model->get_ticket_stats($user_id);
        $data['transaction_stats'] = $this->User_model->get_transaction_stats($user_id);
        
        $this->load->view('dashboard/dashboard_header', $data);
        $this->load->view('dashboard/index', $data);
        $this->load->view('dashboard/dashboard_footer');
    }

    public function profile() {
        $user_id = $this->session->userdata('user_id');
        $data['user'] = $this->User_model->get_user_by_id($user_id);
        
        if ($this->input->post()) {
            $this->load->library('form_validation');
            $this->form_validation->set_rules('full_name', 'Nama Lengkap', 'required');
            $this->form_validation->set_rules('phone', 'Nomor Telepon', 'trim');
            
            if ($this->form_validation->run()) {
                $update_data = [
                    'full_name' => $this->input->post('full_name'),
                    'phone' => $this->input->post('phone')
                ];
                
                if ($this->User_model->update_profile($user_id, $update_data)) {
                    $this->session->set_flashdata('success', 'Profil berhasil diperbarui');
                } else {
                    $this->session->set_flashdata('error', 'Gagal memperbarui profil');
                }
                redirect('dashboard/profile');
            }
        }
        
        $this->load->view('dashboard/dashboard_header', $data);
        $this->load->view('dashboard/profile', $data);
        $this->load->view('dashboard/dashboard_footer');
    }

    public function servers() {
        $user_id = $this->session->userdata('user_id');
        $data['user'] = $this->User_model->get_user($user_id);
        $data['servers'] = $this->Server_model->get_servers($user_id);
        
        $this->load->view('dashboard/dashboard_header', $data);
        $this->load->view('dashboard/servers', $data);
        $this->load->view('dashboard/dashboard_footer');
    }

    public function transactions() {
        $user_id = $this->session->userdata('user_id');
        $data['user'] = $this->User_model->get_user($user_id);
        $data['transactions'] = $this->User_model->get_user_transactions($user_id);
        $data['transaction_stats'] = $this->User_model->get_transaction_stats($user_id);
        
        $this->load->view('dashboard/dashboard_header', $data);
        $this->load->view('dashboard/transactions', $data);
        $this->load->view('dashboard/dashboard_footer');
    }

    public function tickets() {
        $user_id = $this->session->userdata('user_id');
        $data['user'] = $this->User_model->get_user($user_id);
        $data['tickets'] = $this->User_model->get_user_tickets($user_id);
        
        $this->load->view('dashboard/dashboard_header', $data);
        $this->load->view('dashboard/tickets', $data);
        $this->load->view('dashboard/dashboard_footer');
    }

    public function change_password() {
        $user_id = $this->session->userdata('user_id');
        $data['user'] = $this->User_model->get_user($user_id);
        
        if ($this->input->post()) {
            $this->load->library('form_validation');
            $this->form_validation->set_rules('current_password', 'Password Saat Ini', 'required');
            $this->form_validation->set_rules('new_password', 'Password Baru', 'required|min_length[6]');
            $this->form_validation->set_rules('confirm_password', 'Konfirmasi Password', 'required|matches[new_password]');
            
            if ($this->form_validation->run()) {
                $user = $this->User_model->get_user_by_id($user_id);
                
                if (password_verify($this->input->post('current_password'), $user->password)) {
                    if ($this->User_model->change_password($user_id, $this->input->post('new_password'))) {
                        $this->session->set_flashdata('success', 'Password berhasil diubah');
                        redirect('dashboard/profile');
                    } else {
                        $this->session->set_flashdata('error', 'Gagal mengubah password');
                    }
                } else {
                    $this->session->set_flashdata('error', 'Password saat ini salah');
                }
            }
        }
        
        $this->load->view('dashboard/dashboard_header', $data);
        $this->load->view('dashboard/change_password', $data);
        $this->load->view('dashboard/dashboard_footer');
    }
} 