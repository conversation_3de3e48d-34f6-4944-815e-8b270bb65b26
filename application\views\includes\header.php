<?php
// Load settings
if (!isset($GLOBALS['site_settings_loaded'])) {
    include_once VIEWPATH . 'includes/settings_loader.php';
}
?>
	<!-- Navbar -->
	<nav class="navbar" id="navbar">
		<div class="nav-container">
			<a href="<?php echo base_url(); ?>" class="logo">
				<img src="<?php echo base_url(); ?>assets/images/logo.png" alt="<?= site_name() ?>" class="logo-img">
				<?= site_name() ?>.com
			</a>
			<ul class="nav-menu" id="nav-menu">
				<li><a href="<?php echo base_url(); ?>">Home</a></li>
				<li class="dropdown">
				 <a href="#" class="dropdown-toggle">Servers <i class="fas fa-chevron-down"></i></a>
				 <ul class="dropdown-menu">
				  <?php if (isset($categories) && !empty($categories)): ?>
				   <?php foreach ($categories as $category): ?>
				    <li><a href="<?php echo base_url('category/' . $category->slug); ?>"><?php echo html_escape($category->name); ?></a></li>
				   <?php endforeach; ?>
				  <?php endif; ?>
				 </ul>
				</li>
				<li class="dropdown">
					<a href="#support" class="dropdown-toggle">
						Support <i class="fas fa-chevron-down"></i>
					</a>
					<ul class="dropdown-menu">
						<li><a href="#knowledgebase">Knowledge Base</a></li>
						<!-- <li><a href="#tutorials">Video Tutorials</a></li> -->
						<li><a href="#status">Server Status</a></li>
					</ul>
				</li>
				<?php if ($this->session->userdata('user_id')): ?>
					<li class="dropdown">
						<a href="#" class="dropdown-toggle nav-cta">
							<i class="fas fa-user-circle"></i>
							<?= $this->session->userdata('username') ?> <i class="fas fa-chevron-down"></i>
						</a>
						<ul class="dropdown-menu">
							<li><a href="<?php echo base_url('dashboard'); ?>"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
							<?php if (isset($this->session->userdata()['role']) && $this->session->userdata('role') === 'admin'): ?>
							<li><a href="<?php echo base_url('admin'); ?>"><i class="fas fa-user-shield"></i> Admin Panel</a></li>
							<?php endif; ?>
							<li><a href="<?php echo base_url('dashboard/servers'); ?>"><i class="fas fa-server"></i> My Servers</a></li>
							<li><a href="<?php echo base_url('dashboard/transactions'); ?>"><i class="fas fa-money-bill-wave"></i> Transactions</a></li>
							<li><a href="<?php echo base_url('dashboard/tickets'); ?>"><i class="fas fa-ticket-alt"></i> Support Tickets</a></li>
							<li><a href="<?php echo base_url('dashboard/profile'); ?>"><i class="fas fa-user"></i> My Profile</a></li>
							<li class="dropdown-divider"></li>
							<li><a href="<?php echo base_url('auth/logout'); ?>" class="text-danger"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
						</ul>
					</li>
				<?php else: ?>
					<li><a href="<?php echo base_url('auth/login'); ?>" class="nav-cta">
						<i class="fas fa-sign-in-alt"></i>
						Login
					</a></li>
				<?php endif; ?>
			</ul>
			<div class="nav-toggle" id="nav-toggle">
				<span></span>
				<span></span>
				<span></span>
			</div>
		</div>
	</nav> 