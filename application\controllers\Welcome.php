<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Welcome extends CI_Controller {

	/**
	 * Index Page for this controller.
	 *
	 * Maps to the following URL
	 * 		http://example.com/index.php/welcome
	 *	- or -
	 * 		http://example.com/index.php/welcome/index
	 *	- or -
	 * Since this controller is set as the default controller in
	 * config/routes.php, it's displayed at http://example.com/
	 *
	 * So any other public methods not prefixed with an underscore will
	 * map to /index.php/welcome/<method_name>
	 * @see https://codeigniter.com/user_guide/general/urls.html
	 */
	public function index()
	{
		// Load necessary helpers
		$this->load->helper(['url', 'asset']);
		$this->load->library('session');
		
		// Load the home view - accessible to everyone
		$this->load->view('home');
	}
	
	// Debug function to check asset URLs
	public function debug_assets() {
		echo "<h2>Debug Asset URLs</h2>";
		echo "<p>Base URL: " . base_url() . "</p>";
		echo "<p>CSS URL: " . secure_asset('assets/css/style.css') . "</p>";
		echo "<p>JS URL: " . secure_asset('assets/js/main.js') . "</p>";
		echo "<hr>";
		echo "<p>CSS Tag: " . asset_css('assets/css/style.css') . "</p>";
		echo "<p>JS Tag: " . asset_js('assets/js/main.js') . "</p>";
	}
}
