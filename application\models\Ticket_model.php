<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Ticket_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    public function create_ticket($data) {
        // Generate unique ticket number
        $data['ticket_number'] = $this->generate_ticket_number();
        
        $this->db->insert('support_tickets', $data);
        return $this->db->insert_id();
    }

    public function get_tickets($user_id = null, $status = null, $limit = null, $offset = null) {
        $this->db->select('st.*, u.username, u.full_name, u.email');
        $this->db->from('support_tickets st');
        $this->db->join('users u', 'st.user_id = u.id');
        
        if ($user_id) {
            $this->db->where('st.user_id', $user_id);
        }
        
        if ($status) {
            $this->db->where('st.status', $status);
        }
        
        $this->db->order_by('st.updated_at', 'DESC');
        
        if ($limit) {
            $this->db->limit($limit, $offset);
        }
        
        return $this->db->get()->result();
    }

    public function get_ticket($id, $user_id = null) {
        $this->db->select('st.*, u.username, u.full_name, u.email, o.order_number, assigned.username as assigned_username, assigned.full_name as assigned_full_name');
        $this->db->from('support_tickets st');
        $this->db->join('users u', 'st.user_id = u.id');
        $this->db->join('orders o', 'st.order_id = o.id', 'left');
        $this->db->join('users assigned', 'st.assigned_to = assigned.id', 'left');
        $this->db->where('st.id', $id);
        
        if ($user_id) {
            $this->db->where('st.user_id', $user_id);
        }
        
        return $this->db->get()->row();
    }

    public function get_ticket_by_number($ticket_number, $user_id = null) {
        $this->db->select('st.*, u.username, u.full_name');
        $this->db->from('support_tickets st');
        $this->db->join('users u', 'st.user_id = u.id');
        $this->db->where('st.ticket_number', $ticket_number);
        
        if ($user_id) {
            $this->db->where('st.user_id', $user_id);
        }
        
        return $this->db->get()->row();
    }

    public function update_ticket($id, $data) {
        $data['updated_at'] = date('Y-m-d H:i:s');
        $this->db->where('id', $id);
        return $this->db->update('support_tickets', $data);
    }

    public function close_ticket($id) {
        $data = [
            'status' => 'closed',
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $this->db->where('id', $id);
        return $this->db->update('support_tickets', $data);
    }

    // ========== TICKET MESSAGES ==========
    public function add_message($data) {
        $this->db->insert('ticket_messages', $data);
        
        // Update ticket's updated_at timestamp
        $this->db->where('id', $data['ticket_id']);
        $this->db->update('support_tickets', ['updated_at' => date('Y-m-d H:i:s')]);
        
        return $this->db->insert_id();
    }

    public function get_messages($ticket_id) {
        $this->db->select('tm.*, u.username, u.full_name, u.role');
        $this->db->from('ticket_messages tm');
        $this->db->join('users u', 'tm.user_id = u.id');
        $this->db->where('tm.ticket_id', $ticket_id);
        $this->db->where('tm.is_internal', 0); // Only show public messages to users
        $this->db->order_by('tm.created_at', 'ASC');
        
        $messages = $this->db->get()->result();
        
        // Decode attachments JSON
        foreach ($messages as $message) {
            if ($message->attachments) {
                $message->attachments = json_decode($message->attachments, true);
            }
        }
        
        return $messages;
    }

    public function get_all_messages($ticket_id) {
        $this->db->select('ticket_messages.*, users.username, users.role');
        $this->db->from('ticket_messages');
        $this->db->join('users', 'users.id = ticket_messages.user_id', 'left');
        $this->db->where('ticket_messages.ticket_id', $ticket_id);
        $this->db->order_by('ticket_messages.created_at', 'ASC');
        return $this->db->get()->result();
    }

    // ========== HELPER FUNCTIONS ==========
    private function generate_ticket_number() {
        $prefix = 'TKT';
        $date = date('Ymd');
        
        // Get last ticket number for today
        $this->db->like('ticket_number', $prefix . '-' . $date, 'after');
        $this->db->order_by('id', 'DESC');
        $this->db->limit(1);
        $last_ticket = $this->db->get('support_tickets')->row();
        
        if ($last_ticket) {
            $last_number = (int) substr($last_ticket->ticket_number, -4);
            $new_number = $last_number + 1;
        } else {
            $new_number = 1;
        }
        
        return $prefix . '-' . $date . str_pad($new_number, 4, '0', STR_PAD_LEFT);
    }

    // ========== STATISTICS ==========
    public function get_ticket_stats($user_id = null) {
        $this->db->select('
            COUNT(*) as total_tickets,
            COUNT(CASE WHEN status = "open" THEN 1 END) as open_tickets,
            COUNT(CASE WHEN status = "in_progress" THEN 1 END) as in_progress_tickets,
            COUNT(CASE WHEN status = "waiting_customer" THEN 1 END) as waiting_customer_tickets,
            COUNT(CASE WHEN status = "resolved" THEN 1 END) as resolved_tickets,
            COUNT(CASE WHEN status = "closed" THEN 1 END) as closed_tickets
        ');
        
        if ($user_id) {
            $this->db->where('user_id', $user_id);
        }
        
        return $this->db->get('support_tickets')->row();
    }

    public function get_priority_stats($user_id = null) {
        $this->db->select('
            COUNT(CASE WHEN priority = "low" THEN 1 END) as low_priority,
            COUNT(CASE WHEN priority = "normal" THEN 1 END) as normal_priority,
            COUNT(CASE WHEN priority = "high" THEN 1 END) as high_priority,
            COUNT(CASE WHEN priority = "urgent" THEN 1 END) as urgent_priority
        ');
        
        if ($user_id) {
            $this->db->where('user_id', $user_id);
        }
        
        return $this->db->get('support_tickets')->row();
    }

    public function get_category_stats($user_id = null) {
        $this->db->select('category, COUNT(*) as count');
        
        if ($user_id) {
            $this->db->where('user_id', $user_id);
        }
        
        $this->db->group_by('category');
        $this->db->order_by('count', 'DESC');
        
        return $this->db->get('support_tickets')->result();
    }

    public function get_recent_tickets($limit = 10) {
        $this->db->select('support_tickets.*, users.username');
        $this->db->from('support_tickets');
        $this->db->join('users', 'users.id = support_tickets.user_id', 'left');
        $this->db->order_by('support_tickets.created_at', 'DESC');
        $this->db->limit($limit);
        return $this->db->get()->result();
    }

    public function get_unassigned_tickets() {
        $this->db->select('support_tickets.*, users.username');
        $this->db->from('support_tickets');
        $this->db->join('users', 'users.id = support_tickets.user_id', 'left');
        $this->db->where('support_tickets.assigned_to', null);
        $this->db->where('support_tickets.status !=', 'closed');
        $this->db->order_by('support_tickets.created_at', 'ASC');
        return $this->db->get()->result();
    }

    public function assign_ticket($ticket_id, $admin_id) {
        $data = [
            'assigned_to' => $admin_id,
            'status' => 'in_progress',
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $this->db->where('id', $ticket_id);
        return $this->db->update('support_tickets', $data);
    }

    public function count_user_tickets($user_id) {
        return $this->db->where('user_id', $user_id)->count_all_results('support_tickets');
    }

    public function count_total_tickets() {
        return $this->db->count_all('support_tickets');
    }
} 