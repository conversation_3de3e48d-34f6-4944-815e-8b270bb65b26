<?php
defined('BASEPATH') OR exit('No direct script access allowed');

$content = '<div class="page-header">
    <h1>Category Management</h1>
    <p>Manage server categories</p>
</div>';

// Show flash messages
if ($this->session->flashdata('success')) {
    $content .= '<div class="alert alert-success">' . $this->session->flashdata('success') . '</div>';
}
if ($this->session->flashdata('error')) {
    $content .= '<div class="alert alert-danger">' . $this->session->flashdata('error') . '</div>';
}

$content .= '<div class="admin-card">
    <div class="card-header">
        <h2><i class="fas fa-tags"></i> Categories</h2>
        <a href="' . base_url('admin/add_category') . '" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Category
        </a>
    </div>
    <div class="table-responsive">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Icon</th>
                    <th>Plans</th>
                    <th>Sort Order</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>';

if (isset($categories) && !empty($categories)) {
    foreach ($categories as $category) {
        $status_badge = $category->status == 'active' ? 'success' : 'danger';
        $icon_display = $category->icon ? '<i class="' . $category->icon . '"></i>' : '<i class="fas fa-folder"></i>';
        
        $content .= '<tr>
            <td>#' . $category->id . '</td>
            <td>
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    ' . $icon_display . '
                    <span>' . htmlspecialchars($category->name) . '</span>
                </div>
            </td>
            <td><span class="badge info">' . ucfirst($category->type) . '</span></td>
            <td><code>' . ($category->icon ?: 'No icon') . '</code></td>
            <td><span class="badge secondary">' . $category->plan_count . ' plans</span></td>
            <td>' . $category->sort_order . '</td>
            <td><span class="badge ' . $status_badge . '">' . ucfirst($category->status) . '</span></td>
            <td>' . date('M j, Y', strtotime($category->created_at)) . '</td>
            <td>
                <div class="action-buttons">
                    <a href="' . base_url('admin/edit_category/' . $category->id) . '" class="btn btn-sm btn-warning">
                        <i class="fas fa-edit"></i>
                    </a>';
        
        if ($category->plan_count == 0) {
            $content .= '<a href="' . base_url('admin/delete_category/' . $category->id) . '" 
                           class="btn btn-sm btn-danger" 
                           onclick="return confirm(\'Are you sure you want to delete this category?\')">
                        <i class="fas fa-trash"></i>
                    </a>';
        } else {
            $content .= '<button class="btn btn-sm btn-danger" disabled title="Cannot delete category with plans">
                        <i class="fas fa-trash"></i>
                    </button>';
        }
        
        $content .= '</div>
            </td>
        </tr>';
    }
} else {
    $content .= '<tr>
        <td colspan="9" class="text-center">
            <div class="empty-state">
                <i class="fas fa-tags"></i>
                <h3>No Categories Found</h3>
                <p>Start by creating your first category.</p>
                <a href="' . base_url('admin/add_category') . '" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add Category
                </a>
            </div>
        </td>
    </tr>';
}

$content .= '</tbody>
        </table>
    </div>
</div>';

$content .= '<style>
.empty-state {
    padding: 3rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    color: white;
    margin-bottom: 0.5rem;
}

.empty-state p {
    margin-bottom: 1.5rem;
}

.text-center {
    text-align: center;
}

.badge.info {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.badge.secondary {
    background: rgba(156, 163, 175, 0.2);
    color: #9ca3af;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
</style>';

$data['content'] = $content;
$this->load->view('admin/admin_template', $data);
?>
