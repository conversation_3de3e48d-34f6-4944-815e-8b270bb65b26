<?php
defined('BASEPATH') OR exit('No direct script access allowed');
$data['title'] = $title ?? 'Edit User';

$content = '<div class="page-header">
    <h1>Edit User</h1>
    <p>Update user account information</p>
</div>';

// Show validation errors
if (validation_errors()) {
    $content .= '<div class="alert alert-danger">' . validation_errors() . '</div>';
}

if (isset($edit_user) && $edit_user) {
    $content .= '<form method="post" action="' . base_url('admin/edit_user/' . $edit_user->id) . '">
        <div class="admin-card">
            <div class="card-header">
                <h2><i class="fas fa-user-edit"></i> User Information</h2>
            </div>
            <div style="padding: 2rem;">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="username">Username *</label>
                        <input type="text" name="username" id="username" class="form-control" 
                               value="' . set_value('username', $edit_user->username) . '" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email *</label>
                        <input type="email" name="email" id="email" class="form-control" 
                               value="' . set_value('email', $edit_user->email) . '" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="full_name">Full Name</label>
                        <input type="text" name="full_name" id="full_name" class="form-control" 
                               value="' . set_value('full_name', $edit_user->full_name ?? '') . '">
                    </div>
                    
                    <div class="form-group">
                        <label for="password">New Password (leave empty to keep current)</label>
                        <input type="password" name="password" id="password" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label for="role">Role *</label>
                        <select name="role" id="role" class="form-control" required>
                            <option value="user" ' . set_select('role', 'user', $edit_user->role == 'user') . '>User</option>
                            <option value="admin" ' . set_select('role', 'admin', $edit_user->role == 'admin') . '>Admin</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="status">Status *</label>
                        <select name="status" id="status" class="form-control" required>
                            <option value="active" ' . set_select('status', 'active', $edit_user->status == 'active') . '>Active</option>
                            <option value="inactive" ' . set_select('status', 'inactive', $edit_user->status == 'inactive') . '>Inactive</option>
                            <option value="suspended" ' . set_select('status', 'suspended', $edit_user->status == 'suspended') . '>Suspended</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="balance">Balance</label>
                        <input type="number" name="balance" id="balance" class="form-control" 
                               value="' . set_value('balance', $edit_user->balance ?? '0') . '" min="0" step="0.01">
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">Phone Number</label>
                        <input type="text" name="phone" id="phone" class="form-control" 
                               value="' . set_value('phone', $edit_user->phone ?? '') . '">
                    </div>
                </div>
                
                <div class="user-stats" style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1);">
                    <h3 style="color: white; margin-bottom: 1rem;">User Statistics</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">User ID:</span>
                            <span class="stat-value">#' . $edit_user->id . '</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Joined:</span>
                            <span class="stat-value">' . date('M j, Y', strtotime($edit_user->created_at)) . '</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Last Login:</span>
                            <span class="stat-value">' . ($edit_user->last_login ? date('M j, Y H:i', strtotime($edit_user->last_login)) : 'Never') . '</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="form-actions">
            <a href="' . base_url('admin/users') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Users
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Update User
            </button>
        </div>
    </form>';
} else {
    $content .= '<div class="alert alert-danger">User not found.</div>';
}

$content .= '<style>
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.form-actions {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}
.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
}
.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
}
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}
.stat-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}
.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}
.stat-value {
    color: white;
    font-weight: 600;
}
</style>';

$data['content'] = $content;
$this->load->view('admin/admin_template', $data);
?> 