<?php
defined('BASEPATH') OR exit('No direct script access allowed');

$content = '<div class="page-header">
    <h1>Edit Category</h1>
    <p>Update category information</p>
</div>';

// Show validation errors
if (validation_errors()) {
    $content .= '<div class="alert alert-danger">' . validation_errors() . '</div>';
}

if (isset($category) && $category) {
    $content .= '<form method="post" action="' . base_url('admin/edit_category/' . $category->id) . '">
        <div class="admin-card">
            <div class="card-header">
                <h2><i class="fas fa-tag"></i> Category Information</h2>
            </div>
            <div style="padding: 2rem;">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="name">Category Name *</label>
                        <input type="text" name="name" id="name" class="form-control" 
                               value="' . set_value('name', $category->name) . '" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="type">Category Type *</label>
                        <select name="type" id="type" class="form-control" required>
                            <option value="">Select Type</option>
                            <option value="game" ' . set_select('type', 'game', $category->type == 'game') . '>Game Server</option>
                            <option value="vps" ' . set_select('type', 'vps', $category->type == 'vps') . '>VPS/Cloud</option>
                            <option value="web" ' . set_select('type', 'web', $category->type == 'web') . '>Web Hosting</option>
                            <option value="dedicated" ' . set_select('type', 'dedicated', $category->type == 'dedicated') . '>Dedicated Server</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="slug">Slug</label>
                        <input type="text" name="slug" id="slug" class="form-control" 
                               value="' . set_value('slug', $category->slug) . '" readonly>
                        <small class="form-text">Slug cannot be changed after creation</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="icon">Icon Class</label>
                        <input type="text" name="icon" id="icon" class="form-control" 
                               value="' . set_value('icon', $category->icon ?? '') . '" placeholder="e.g., fas fa-gamepad">
                        <small class="form-text">FontAwesome icon class (optional)</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="sort_order">Sort Order</label>
                        <input type="number" name="sort_order" id="sort_order" class="form-control" 
                               value="' . set_value('sort_order', $category->sort_order ?? '0') . '" min="0">
                    </div>
                    
                    <div class="form-group">
                        <label for="status">Status *</label>
                        <select name="status" id="status" class="form-control" required>
                            <option value="active" ' . set_select('status', 'active', $category->status == 'active') . '>Active</option>
                            <option value="inactive" ' . set_select('status', 'inactive', $category->status == 'inactive') . '>Inactive</option>
                        </select>
                    </div>
                    
                    <div class="form-group full-width">
                        <label for="description">Description</label>
                        <textarea name="description" id="description" class="form-control" rows="3">' . set_value('description', $category->description ?? '') . '</textarea>
                    </div>
                </div>
                
                <div class="category-stats" style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1);">
                    <h3 style="color: white; margin-bottom: 1rem;">Category Statistics</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">Category ID:</span>
                            <span class="stat-value">#' . $category->id . '</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Created:</span>
                            <span class="stat-value">' . date('M j, Y', strtotime($category->created_at)) . '</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Last Updated:</span>
                            <span class="stat-value">' . (isset($category->updated_at) ? date('M j, Y H:i', strtotime($category->updated_at)) : 'Never') . '</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="form-actions">
            <a href="' . base_url('admin/categories') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Categories
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Update Category
            </button>
        </div>
    </form>';
} else {
    $content .= '<div class="alert alert-danger">Category not found.</div>';
}

$content .= '<style>
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}
.form-group.full-width {
    grid-column: 1 / -1;
}
.form-actions {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}
.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
}
.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
}
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}
.stat-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}
.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}
.stat-value {
    color: white;
    font-weight: 600;
}
</style>';

$data['content'] = $content;
$this->load->view('admin/admin_template', $data);
?> 