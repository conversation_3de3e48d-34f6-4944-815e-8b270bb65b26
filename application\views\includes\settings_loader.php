<?php
// Settings Loader for Views
// Include this file at the top of views that need settings

if (!isset($GLOBALS['site_settings_loaded'])) {
    $CI =& get_instance();
    
    if (!isset($CI->db)) {
        $CI->load->database();
    }
    
    // Load settings from database
    $settings = [];
    if ($CI->db->table_exists('settings')) {
        $query = $CI->db->get('settings');
        if ($query->num_rows() > 0) {
            foreach ($query->result() as $setting) {
                $settings[$setting->key] = $setting->value;
            }
        }
    }
    
    // Set defaults
    $defaults = [
        'site_name' => 'Dopminer',
        'site_description' => 'Premium Game Server Hosting',
        'site_email' => '<EMAIL>',
        'site_phone' => '+62 812-8115-9899',
        'currency' => 'IDR',
        'currency_symbol' => 'Rp'
    ];
    
    foreach ($defaults as $key => $value) {
        if (!isset($settings[$key])) {
            $settings[$key] = $value;
        }
    }
    
    // Make available
    $GLOBALS['site_settings'] = $settings;
    $GLOBALS['site_settings_loaded'] = true;
}

// Helper functions
if (!function_exists('get_site_setting')) {
    function get_site_setting($key, $default = null) {
        return isset($GLOBALS['site_settings'][$key]) ? $GLOBALS['site_settings'][$key] : $default;
    }
}

if (!function_exists('site_name')) {
    function site_name() {
        return get_site_setting('site_name', 'Dopminer');
    }
}

if (!function_exists('site_description')) {
    function site_description() {
        return get_site_setting('site_description', 'Premium Game Server Hosting');
    }
}

if (!function_exists('currency_symbol')) {
    function currency_symbol() {
        return get_site_setting('currency_symbol', 'Rp');
    }
}

if (!function_exists('format_currency')) {
    function format_currency($amount) {
        $symbol = currency_symbol();
        return $symbol . ' ' . number_format($amount, 0, ',', '.');
    }
}

?>
 