<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Home extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->helper(['url', 'asset']);
        $this->load->library('session');
        $this->load->model('Server_model');
    }

    public function index() {
        // Load the home view - this is public and doesn't require login
        $data['categories'] = $this->Server_model->get_categories();
        $this->load->view('home', $data);
    }
} 