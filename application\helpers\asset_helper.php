<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Asset Helper with Security Protection
 * Generates secure URLs for CSS and JS files
 */

if (!function_exists('secure_asset')) {
    /**
     * Generate secure asset URL with token
     * 
     * @param string $asset_path Path to asset file
     * @return string Secure URL with token
     */
    function secure_asset($asset_path) {
        $CI =& get_instance();
        $base_url = $CI->config->item('base_url');
        
        // Parse asset path
        $path_info = pathinfo($asset_path);
        $type = $path_info['extension']; // css or js
        $filename = $path_info['basename'];
        
        // Generate timestamp-based token
        $timestamp = time();
        $secret_key = 'DopMiner_Secret_2024'; // Change this to your secret
        $token = md5($asset_path . $timestamp . $secret_key);
        
        // Create secure URL via controller
        $secure_url = $base_url . 'asset/' . $type . '/' . $filename . '?t=' . $timestamp . '&token=' . substr($token, 0, 16);
        
        return $secure_url;
    }
}

if (!function_exists('asset_css')) {
    /**
     * Generate secure CSS link tag
     * 
     * @param string $css_file CSS file path (relative to base_url)
     * @return string HTML link tag
     */
    function asset_css($css_file) {
        $secure_url = secure_asset($css_file);
        return '<link href="' . $secure_url . '" rel="stylesheet">';
    }
}

if (!function_exists('asset_js')) {
    /**
     * Generate secure JS script tag
     * 
     * @param string $js_file JS file path (relative to base_url)
     * @return string HTML script tag
     */
    function asset_js($js_file) {
        $secure_url = secure_asset($js_file);
        return '<script src="' . $secure_url . '"></script>';
    }
}

if (!function_exists('obfuscate_css')) {
    /**
     * Minify and obfuscate CSS content
     * 
     * @param string $css_content CSS content
     * @return string Minified CSS
     */
    function obfuscate_css($css_content) {
        // Remove comments
        $css_content = preg_replace('/\/\*.*?\*\//s', '', $css_content);
        
        // Remove unnecessary whitespace
        $css_content = preg_replace('/\s+/', ' ', $css_content);
        
        // Remove whitespace around specific characters
        $css_content = str_replace(['; ', ' {', '{ ', ' }', '} ', ': ', ' :'], [';', '{', '{', '}', '}', ':', ':'], $css_content);
        
        return trim($css_content);
    }
}

if (!function_exists('obfuscate_js')) {
    /**
     * Basic JS obfuscation
     * 
     * @param string $js_content JS content
     * @return string Obfuscated JS
     */
    function obfuscate_js($js_content) {
        // Remove single-line comments
        $js_content = preg_replace('/\/\/.*$/m', '', $js_content);
        
        // Remove multi-line comments
        $js_content = preg_replace('/\/\*.*?\*\//s', '', $js_content);
        
        // Remove unnecessary whitespace
        $js_content = preg_replace('/\s+/', ' ', $js_content);
        
        // Remove whitespace around operators
        $js_content = str_replace([' = ', ' + ', ' - ', ' * ', ' / ', ' { ', ' } ', ' ( ', ' ) ', ' ; '], ['=', '+', '-', '*', '/', '{', '}', '(', ')', ';'], $js_content);
        
        return trim($js_content);
    }
} 