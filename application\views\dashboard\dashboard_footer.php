                </div><!-- /.container-fluid -->
            </div><!-- /.main-content -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
    
    <?php $this->load->view('includes/footer'); ?>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="<?php echo secure_asset('assets/js/main.js'); ?>"></script>
    
    	<script>
		// Loading animation with progress bar
		document.addEventListener('DOMContentLoaded', function() {
			const progressBar = document.getElementById('loadingProgress');
			const loadingOverlay = document.getElementById('loadingOverlay');
			
			// Simulate loading progress
			let progress = 0;
			const progressInterval = setInterval(function() {
				progress += Math.random() * 15;
				if (progress > 100) progress = 100;
				
				if (progressBar) {
					progressBar.style.width = progress + '%';
				}
				
				if (progress >= 100) {
					clearInterval(progressInterval);
					
					setTimeout(function() {
						if (loadingOverlay) {
							loadingOverlay.classList.add('fade-out');
							setTimeout(function() {
								loadingOverlay.remove();
								if (progressBar) progressBar.remove();
							}, 500);
						}
					}, 300);
				}
			}, 100);
		});
		
		// Counter animation
		function animateCounter(element, target, duration = 2000) {
			let start = 0;
			const increment = target / (duration / 16);
			
			function updateCounter() {
				start += increment;
				if (start < target) {
					element.textContent = Math.floor(start).toLocaleString();
					requestAnimationFrame(updateCounter);
				} else {
					element.textContent = target.toLocaleString();
				}
			}
			
			updateCounter();
		}
		
		// Typing effect for welcome message
		function typeWriter(element, text, speed = 100) {
			element.innerHTML = '';
			let i = 0;
			function typing() {
				if (i < text.length) {
					element.innerHTML += text.charAt(i);
					i++;
					setTimeout(typing, speed);
				}
			}
			typing();
		}
		
			
			// Start typing effect for welcome message
			setTimeout(function() {
				const welcomeElement = document.getElementById('welcomeText');
				if (welcomeElement) {
					const originalText = welcomeElement.textContent;
					typeWriter(welcomeElement, originalText, 80);
				}
			}, 1200);
			
			// Start counter animations
			setTimeout(function() {
				const counters = document.querySelectorAll('.counter');
				counters.forEach(function(counter) {
					const target = parseInt(counter.getAttribute('data-target'));
					if (target) {
						animateCounter(counter, target);
					}
				});
			}, 1500);
		
		// Flash messages auto-hide
		setTimeout(function() {
			const alerts = document.querySelectorAll('.alert');
			alerts.forEach(function(alert) {
				alert.style.opacity = '0';
				setTimeout(function() {
					alert.remove();
				}, 300);
			});
		}, 5000);
		
		// User dropdown toggle
		function toggleUserMenu() {
			const dropdown = document.getElementById('userDropdown');
			if (dropdown) {
				dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
			}
		}
		
		// Close dropdown when clicking outside
		document.addEventListener('click', function(event) {
			const userMenu = document.querySelector('.user-menu');
			const dropdown = document.getElementById('userDropdown');
			
			if (userMenu && dropdown && !userMenu.contains(event.target)) {
				dropdown.style.display = 'none';
			}
		});
		
		// Add hover sound effect (optional)
		document.querySelectorAll('.stat-card').forEach(function(card) {
			card.addEventListener('mouseenter', function() {
				this.style.transform = 'translateY(-8px) scale(1.02)';
			});
			
			card.addEventListener('mouseleave', function() {
				this.style.transform = 'translateY(0) scale(1)';
			});
		});
		
		// Staggered table row animations
		setTimeout(function() {
			const tableRows = document.querySelectorAll('.dashboard-table tbody tr');
			tableRows.forEach(function(row, index) {
				row.style.opacity = '0';
				row.style.transform = 'translateX(-20px)';
				row.style.transition = 'all 0.3s ease';
				
				setTimeout(function() {
					row.style.opacity = '1';
					row.style.transform = 'translateX(0)';
				}, 1500 + (index * 100));
			});
		}, 100);
		
		// Add sparkle effect to stat cards
		function createSparkle(element) {
			const sparkle = document.createElement('div');
			sparkle.className = 'sparkle';
			sparkle.style.cssText = `
				position: absolute;
				width: 4px;
				height: 4px;
				background: #fff;
				border-radius: 50%;
				pointer-events: none;
				animation: sparkleAnimation 1s ease-out forwards;
			`;
			
			const rect = element.getBoundingClientRect();
			sparkle.style.left = Math.random() * rect.width + 'px';
			sparkle.style.top = Math.random() * rect.height + 'px';
			
			element.appendChild(sparkle);
			
			setTimeout(() => sparkle.remove(), 1000);
		}
		
		// Add sparkle effect on hover
		document.querySelectorAll('.stat-card').forEach(function(card) {
			card.addEventListener('mouseenter', function() {
				this.style.position = 'relative';
				this.style.overflow = 'hidden';
				createSparkle(this);
				
				// Create multiple sparkles
				setTimeout(() => createSparkle(this), 200);
				setTimeout(() => createSparkle(this), 400);
			});
		});
		
		// Add CSS for sparkle animation
		const sparkleStyle = document.createElement('style');
		sparkleStyle.textContent = `
			@keyframes sparkleAnimation {
				0% {
					opacity: 0;
					transform: scale(0) rotate(0deg);
				}
				50% {
					opacity: 1;
					transform: scale(1) rotate(180deg);
				}
				100% {
					opacity: 0;
					transform: scale(0) rotate(360deg);
				}
			}
		`;
		document.head.appendChild(sparkleStyle);
	</script>
</body>
</html> 