:root {
    --primary-blue: #0ea5e9;
    --secondary-blue: #3b82f6;
    --dark-blue: #1e40af;
    --light-blue: #e0f2fe;
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    --black: #000000;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
    color: var(--white);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Navbar */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(15, 23, 42, 0.85);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    background: rgba(15, 23, 42, 0.95);
    border-bottom-color: rgba(59, 130, 246, 0.2);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.logo {
    font-family: 'JetBrains Mono', monospace;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-blue);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
}

.logo:hover {
    color: var(--secondary-blue);
    transform: translateY(-1px);
}

.logo i {
    font-size: 1.8rem;
    color: var(--white);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
    align-items: center;
}

.nav-menu li {
    position: relative;
}

.nav-menu a {
    color: var(--gray-300);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-menu a:hover {
    color: var(--white);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background: var(--primary-blue);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

/* Dropdown Menu */
.dropdown {
    position: relative;
}

.dropdown-toggle i {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.dropdown:hover .dropdown-toggle i {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--gray-900);
    border: 1px solid var(--gray-700);
    border-radius: 8px;
    min-width: 200px;
    padding: 0.5rem 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    z-index: 1001;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    list-style: none;
}

.dropdown-menu a {
    display: block;
    padding: 0.75rem 1.5rem;
    color: var(--gray-300);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border-bottom: none;
}

.dropdown-menu a::after {
    display: none;
}

.dropdown-menu a:hover {
    background: var(--gray-800);
    color: var(--primary-blue);
    padding-left: 2rem;
}

.nav-cta {
    background: var(--primary-blue) !important;
    color: var(--white) !important;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.nav-cta:hover {
    background: var(--secondary-blue) !important;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(14, 165, 233, 0.25);
}

.nav-cta::after {
    display: none;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
}

.nav-toggle span {
    width: 25px;
    height: 2px;
    background: var(--white);
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding-top: 100px;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 50%, rgba(14, 165, 233, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(14, 165, 233, 0.05) 0%, transparent 50%);
    z-index: 1;
}

.hero-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px);
    background-size: 60px 60px;
    z-index: 1;
}

.hero-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1.2fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-content {
    max-width: 600px;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(14, 165, 233, 0.1);
    border: 1px solid rgba(14, 165, 233, 0.2);
    color: var(--primary-blue);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, var(--white) 0%, var(--gray-300) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-content .highlight {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-content p {
    font-size: 1.25rem;
    color: var(--gray-400);
    margin-bottom: 2.5rem;
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    box-shadow: 0 4px 15px rgba(14, 165, 233, 0.25);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(14, 165, 233, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--gray-600);
}

.btn-secondary:hover {
    border-color: var(--primary-blue);
    color: var(--primary-blue);
    transform: translateY(-2px);
}

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.server-showcase {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
    max-width: 320px;
    margin: 0 auto;
}

.server-card {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(15, 23, 42, 0.95) 100%);
    border: 1px solid var(--gray-700);
    border-radius: 12px;
    padding: 1.25rem;
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    width: 100%;
    opacity: 0.8;
}

.server-card.active {
    border-color: var(--primary-blue);
    box-shadow: 0 12px 40px rgba(14, 165, 233, 0.2);
    transform: translateY(-3px);
    opacity: 1;
}

.server-card:nth-child(1) {
    order: 1;
}

.server-card:nth-child(2) {
    order: 2;
}

.server-card:nth-child(3) {
    order: 3;
}

.server-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-blue);
    box-shadow: 0 16px 32px rgba(14, 165, 233, 0.2);
    opacity: 1;
}

.server-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.server-icon {
    width: 32px;
    height: 32px;
    background: var(--primary-blue);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 0.85rem;
    flex-shrink: 0;
}

.server-info {
    flex: 1;
    min-width: 0;
}

.server-info h3 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.15rem;
    color: var(--white);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.server-info p {
    font-size: 0.75rem;
    color: var(--gray-400);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.server-stats {
    display: flex;
    gap: 0.5rem;
    justify-content: space-between;
}

.server-stat {
    text-align: center;
    flex: 1;
    min-width: 0;
}

.server-stat .stat-value {
    font-size: 1rem;
    font-weight: 700;
    color: var(--primary-blue);
    display: block;
    margin-bottom: 0.15rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.server-stat .stat-label {
    font-size: 0.6rem;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.3px;
    line-height: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Stats Section */
.stats {
    padding: 6rem 0;
    background: var(--gray-900);
}

.stats-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
}

.stat-text {
    font-size: 1.1rem;
    color: var(--gray-400);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Features Section */
.features {
    padding: 8rem 0;
    background: var(--gray-800);
    position: relative;
}

.features::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-blue), transparent);
}

.features-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.section-header {
    text-align: center;
    margin-bottom: 5rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--white);
}

.section-header p {
    font-size: 1.2rem;
    color: var(--gray-400);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
    border: 1px solid var(--gray-700);
    border-radius: 16px;
    padding: 2.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-blue), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-8px);
    border-color: var(--primary-blue);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.feature-icon i {
    font-size: 1.5rem;
    color: var(--white);
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--white);
}

.feature-card p {
    color: var(--gray-400);
    line-height: 1.6;
}

/* Footer */
.footer {
    background: var(--black);
    padding: 4rem 0 2rem;
    border-top: 1px solid var(--gray-800);
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1.5fr repeat(3, 1fr);
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-brand h3 {
    font-family: 'JetBrains Mono', monospace;
    font-size: 1.5rem;
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.footer-brand p {
    color: var(--gray-400);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--gray-800);
    border: 1px solid var(--gray-700);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
    color: var(--white);
    transform: translateY(-2px);
}

.footer-section h4 {
    color: var(--white);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.footer-section a {
    display: block;
    color: var(--gray-400);
    text-decoration: none;
    margin-bottom: 0.75rem;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--primary-blue);
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--gray-500);
    font-size: 0.9rem;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: var(--gray-500);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-blue);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-content {
        max-width: none;
    }

    .server-showcase {
        max-width: 280px;
        gap: 0.75rem;
    }

    .server-card {
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 100%;
        flex-direction: column;
        background: var(--gray-900);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        padding: 2rem 0;
        border-top: 1px solid var(--gray-700);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu .dropdown-menu {
        position: static;
        background: var(--gray-800);
        border: none;
        box-shadow: none;
        opacity: 1;
        visibility: visible;
        transform: none;
        display: none;
        margin-top: 0.5rem;
    }

    .nav-menu .dropdown.active .dropdown-menu {
        display: block;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 1rem;
    }

    .hero-container {
        padding: 0 1rem;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .btn {
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .server-showcase {
        max-width: 260px;
        gap: 0.5rem;
    }

    .server-card {
        padding: 0.875rem;
    }

    .server-stat .stat-value {
        font-size: 0.9rem;
    }

    .server-stat .stat-label {
        font-size: 0.55rem;
    }

    .server-info h3 {
        font-size: 0.8rem;
    }

    .server-info p {
        font-size: 0.7rem;
    }

    .server-icon {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }
} 