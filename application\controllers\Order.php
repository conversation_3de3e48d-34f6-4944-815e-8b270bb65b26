<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Order extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('Server_model');
        $this->load->model('User_model');
        $this->load->library('session');
        
        // Check if user is logged in
        if (!$this->session->userdata('user_id')) {
            redirect('auth/login');
        }
    }

    public function index() {
        $data['title'] = 'Browse Servers';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        
        // Get server categories
        $data['game_categories'] = $this->Server_model->get_categories('game');
        $data['vps_categories'] = $this->Server_model->get_categories('vps');
        
        // Get featured plans
        $data['featured_plans'] = $this->Server_model->get_plans();
        
        $this->load->view('includes/header', $data);
        $this->load->view('order/browse', $data);
        $this->load->view('includes/footer');
    }

    public function category($slug) {
        $category = $this->Server_model->get_category_by_slug($slug);
        
        if (!$category) {
            show_404();
        }
        
        $data['title'] = $category->name . ' - Server Plans';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        $data['category'] = $category;
        $data['plans'] = $this->Server_model->get_plans($category->id);
        
        $this->load->view('includes/header', $data);
        $this->load->view('order/category', $data);
        $this->load->view('includes/footer');
    }

    public function configure($plan_slug) {
        $plan = $this->Server_model->get_plan_by_slug($plan_slug);
        
        if (!$plan) {
            show_404();
        }
        
        $data['title'] = 'Configure ' . $plan->name;
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        $data['plan'] = $plan;
        
        $this->load->view('includes/header', $data);
        $this->load->view('order/configure', $data);
        $this->load->view('includes/footer');
    }

    public function checkout() {
        if ($this->input->method() !== 'post') {
            redirect('order');
        }
        
        $plan_id = $this->input->post('plan_id');
        $server_name = $this->input->post('server_name');
        $billing_cycle = $this->input->post('billing_cycle');
        
        // Validate inputs
        $this->form_validation->set_rules('plan_id', 'Plan', 'required|numeric');
        $this->form_validation->set_rules('server_name', 'Server Name', 'required|min_length[3]|max_length[50]|alpha_dash');
        $this->form_validation->set_rules('billing_cycle', 'Billing Cycle', 'required|in_list[monthly,quarterly,yearly]');
        
        if (!$this->form_validation->run()) {
            $this->session->set_flashdata('error', validation_errors());
            redirect('order');
        }
        
        $plan = $this->Server_model->get_plan($plan_id);
        if (!$plan) {
            $this->session->set_flashdata('error', 'Invalid server plan selected.');
            redirect('order');
        }
        
        $user = $this->User_model->get_user($this->session->userdata('user_id'));
        
        // Calculate pricing
        $amount = $this->Server_model->get_price_for_cycle($plan, $billing_cycle);
        $setup_fee = $plan->setup_fee;
        $total_amount = $amount + $setup_fee;
        
        $data['title'] = 'Checkout';
        $data['user'] = $user;
        $data['plan'] = $plan;
        $data['server_name'] = $server_name;
        $data['billing_cycle'] = $billing_cycle;
        $data['amount'] = $amount;
        $data['setup_fee'] = $setup_fee;
        $data['total_amount'] = $total_amount;
        
        $this->load->view('includes/header', $data);
        $this->load->view('order/checkout', $data);
        $this->load->view('includes/footer');
    }

    public function process() {
        if ($this->input->method() !== 'post') {
            redirect('order');
        }
        
        $plan_id = $this->input->post('plan_id');
        $server_name = $this->input->post('server_name');
        $billing_cycle = $this->input->post('billing_cycle');
        $payment_method = $this->input->post('payment_method');
        
        // Validate inputs
        $this->form_validation->set_rules('plan_id', 'Plan', 'required|numeric');
        $this->form_validation->set_rules('server_name', 'Server Name', 'required|min_length[3]|max_length[50]|alpha_dash');
        $this->form_validation->set_rules('billing_cycle', 'Billing Cycle', 'required|in_list[monthly,quarterly,yearly]');
        $this->form_validation->set_rules('payment_method', 'Payment Method', 'required|in_list[balance,bank_transfer]');
        
        if (!$this->form_validation->run()) {
            $this->session->set_flashdata('error', validation_errors());
            redirect('order/checkout');
        }
        
        $plan = $this->Server_model->get_plan($plan_id);
        if (!$plan) {
            $this->session->set_flashdata('error', 'Invalid server plan selected.');
            redirect('order');
        }
        
        $user = $this->User_model->get_user($this->session->userdata('user_id'));
        
        // Calculate pricing
        $amount = $this->Server_model->get_price_for_cycle($plan, $billing_cycle);
        $setup_fee = $plan->setup_fee;
        $total_amount = $amount + $setup_fee;
        
        // Check if using balance payment
        if ($payment_method === 'balance') {
            if ($user->balance < $total_amount) {
                $this->session->set_flashdata('error', 'Insufficient balance. Please top up your account first.');
                redirect('order/checkout');
            }
        }
        
        // Create order
        $order_data = [
            'user_id' => $user->id,
            'plan_id' => $plan_id,
            'server_name' => $server_name,
            'billing_cycle' => $billing_cycle,
            'amount' => $amount,
            'setup_fee' => $setup_fee,
            'total_amount' => $total_amount,
            'payment_status' => ($payment_method === 'balance') ? 'paid' : 'pending',
            'server_status' => 'pending',
            'expires_at' => $this->Server_model->calculate_expiry_date($billing_cycle)
        ];
        
        $order_id = $this->Server_model->create_order($order_data);
        
        if ($order_id) {
            // Process payment
            if ($payment_method === 'balance') {
                // Deduct balance
                $new_balance = $user->balance - $total_amount;
                $this->User_model->update_balance($user->id, $new_balance);
                
                // Create transaction record
                $transaction_data = [
                    'user_id' => $user->id,
                    'order_id' => $order_id,
                    'transaction_id' => 'TXN' . date('YmdHis') . $user->id,
                    'type' => 'purchase',
                    'amount' => $total_amount,
                    'balance_before' => $user->balance,
                    'balance_after' => $new_balance,
                    'status' => 'success',
                    'payment_method' => 'balance',
                    'description' => 'Server order: ' . $server_name
                ];
                
                $this->User_model->create_transaction($transaction_data);
                
                // Update order status
                $this->Server_model->update_order($order_id, ['server_status' => 'setting_up']);
                
                // Create server record (simplified - in production this would trigger actual server provisioning)
                $server_data = [
                    'order_id' => $order_id,
                    'user_id' => $user->id,
                    'plan_id' => $plan_id,
                    'server_name' => $server_name,
                    'status' => 'active',
                    'next_due_date' => date('Y-m-d', strtotime($this->Server_model->calculate_expiry_date($billing_cycle)))
                ];
                
                $this->Server_model->create_server($server_data);
                
                $this->session->set_flashdata('success', 'Order placed successfully! Your server is being set up.');
                redirect('dashboard/servers');
            } else {
                // For bank transfer, redirect to payment instructions
                $this->session->set_flashdata('success', 'Order created! Please complete the payment to activate your server.');
                redirect('order/payment/' . $order_id);
            }
        } else {
            $this->session->set_flashdata('error', 'Failed to create order. Please try again.');
            redirect('order/checkout');
        }
    }

    public function payment($order_id) {
        $order = $this->Server_model->get_order($order_id, $this->session->userdata('user_id'));
        
        if (!$order || $order->payment_status === 'paid') {
            redirect('dashboard/servers');
        }
        
        $data['title'] = 'Payment Instructions';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        $data['order'] = $order;
        
        $this->load->view('includes/header', $data);
        $this->load->view('order/payment', $data);
        $this->load->view('includes/footer');
    }

    public function history() {
        $data['title'] = 'Order History';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        
        // Pagination
        $this->load->library('pagination');
        $config['base_url'] = base_url('order/history');
        $config['total_rows'] = count($this->Server_model->get_orders($this->session->userdata('user_id')));
        $config['per_page'] = 10;
        $config['uri_segment'] = 3;
        
        $this->pagination->initialize($config);
        
        $page = ($this->uri->segment(3)) ? $this->uri->segment(3) : 0;
        $data['orders'] = $this->Server_model->get_orders($this->session->userdata('user_id'), $config['per_page'], $page);
        $data['pagination'] = $this->pagination->create_links();
        
        $this->load->view('includes/header', $data);
        $this->load->view('order/history', $data);
        $this->load->view('includes/footer');
    }

    public function view($order_id) {
        $order = $this->Server_model->get_order($order_id, $this->session->userdata('user_id'));
        
        if (!$order) {
            show_404();
        }
        
        $data['title'] = 'Order #' . $order->order_number;
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        $data['order'] = $order;
        
        $this->load->view('includes/header', $data);
        $this->load->view('order/view', $data);
        $this->load->view('includes/footer');
    }
} 