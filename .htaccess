RewriteEngine On
RewriteBase /dopminer/

# Handle Angular and HTML5 mode
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php/$1 [L,QSA]

# Remove index.php from URL
RewriteCond %{THE_REQUEST} /index\.php/([^\s\?]*) [NC]
RewriteRule ^index\.php/(.*)$ $1 [R=302,L]

# Security measures
<Files "*.php">
Order Deny,Allow
Allow from all
</Files>

<FilesMatch "^(application|system|\.ht)">
Order Deny,Allow
Deny from all
</FilesMatch> 