<?php
defined('BASEPATH') OR exit('No direct script access allowed');
// Load settings
if (!isset($GLOBALS['site_settings_loaded'])) {
    include_once VIEWPATH . 'includes/settings_loader.php';
}
?>

<div class="category-browse-container">
    <!-- Category Header -->
    <div class="category-header">
        <div class="container">
            <div class="category-header-content">
                <div class="category-info">
                    <div class="category-icon">
                        <?php if (isset($category->icon) && !empty($category->icon)): ?>
                            <i class="<?= $category->icon ?>"></i>
                        <?php else: ?>
                            <i class="fas fa-server"></i>
                        <?php endif; ?>
                    </div>
                    <div class="category-details">
                        <h1><?= $category->name ?> Hosting</h1>
                        <p><?= $category->description ?: 'Premium hosting solutions for ' . $category->name ?></p>
                        <div class="category-meta">
                            <span class="meta-item">
                                <i class="fas fa-shield-alt"></i>
                                DDoS Protection
                            </span>
                            <span class="meta-item">
                                <i class="fas fa-headset"></i>
                                24/7 Support
                            </span>
                            <span class="meta-item">
                                <i class="fas fa-rocket"></i>
                                Instant Setup
                            </span>
                        </div>
                    </div>
                </div>
                <div class="category-actions">
                    <a href="<?= base_url('order') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Back to Browse
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Plans Section -->
    <div class="plans-section">
        <div class="container">
            <?php if (isset($plans) && !empty($plans)): ?>
                <div class="plans-grid">
                    <?php foreach ($plans as $plan): ?>
                    <div class="plan-card" data-plan="<?= $plan->id ?>">
                        <div class="plan-header">
                            <div class="plan-category">
                                <?= strtoupper($category->name) ?>
                            </div>
                            <h3 class="plan-name"><?= $plan->name ?></h3>
                            <div class="plan-description">
                                <?= $plan->description ?>
                            </div>
                        </div>

                        <div class="plan-pricing">
                            <div class="price-monthly">
                                <span class="price-amount"><?= format_currency($plan->price_monthly) ?></span>
                                <span class="price-period">/month</span>
                            </div>
                            <?php if ($plan->price_quarterly && $plan->price_quarterly < ($plan->price_monthly * 3)): ?>
                            <div class="price-quarterly">
                                <span class="price-amount"><?= format_currency($plan->price_quarterly) ?></span>
                                <span class="price-period">/3 months</span>
                                <span class="price-save">Save <?= number_format((($plan->price_monthly * 3) - $plan->price_quarterly) / ($plan->price_monthly * 3) * 100, 0) ?>%</span>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="plan-specs">
                            <h4>Specifications</h4>
                            <?php if (isset($plan->specifications) && is_array($plan->specifications)): ?>
                                <?php foreach ($plan->specifications as $key => $value): ?>
                                <div class="spec-item">
                                    <i class="fas fa-check"></i>
                                    <span><?= ucfirst(str_replace('_', ' ', $key)) ?>: <?= $value ?></span>
                                </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>

                        <div class="plan-features">
                            <h4>Features</h4>
                            <?php if (isset($plan->features) && is_array($plan->features)): ?>
                                <?php foreach ($plan->features as $feature): ?>
                                <div class="feature-item">
                                    <i class="fas fa-star"></i>
                                    <span><?= $feature ?></span>
                                </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>

                        <div class="plan-actions">
                            <a href="<?= base_url('order/configure/' . $plan->slug) ?>" class="btn btn-primary">
                                <i class="fas fa-shopping-cart"></i>
                                Order Now
                            </a>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <h3>No Plans Available</h3>
                    <p>There are currently no hosting plans available for <?= $category->name ?>.</p>
                    <a href="<?= base_url('order') ?>" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i>
                        Browse Other Categories
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.category-browse-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding-top: 2rem;
}

.category-header {
    padding: 4rem 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.category-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.category-info {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.category-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.category-details h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin: 0 0 0.5rem 0;
}

.category-details p {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0 0 1rem 0;
}

.category-meta {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.875rem;
}

.meta-item i {
    color: #0ea5e9;
}

.plans-section {
    padding: 4rem 0;
}

.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.plan-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.plan-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.plan-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 60px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.plan-header {
    text-align: center;
    margin-bottom: 2rem;
}

.plan-category {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 1rem;
    text-transform: uppercase;
}

.plan-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0 0 0.5rem 0;
}

.plan-description {
    color: #718096;
    font-size: 0.875rem;
    line-height: 1.6;
}

.plan-pricing {
    margin-bottom: 2rem;
    text-align: center;
}

.price-monthly, .price-quarterly {
    margin-bottom: 0.5rem;
}

.price-amount {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
}

.price-period {
    color: #718096;
    font-size: 1rem;
}

.price-save {
    background: #22c55e;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

.plan-specs, .plan-features {
    margin-bottom: 2rem;
}

.plan-specs h4, .plan-features h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.spec-item, .feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: #4a5568;
    font-size: 0.875rem;
}

.spec-item i, .feature-item i {
    color: #667eea;
    width: 16px;
    flex-shrink: 0;
}

.plan-actions {
    text-align: center;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 0.875rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.empty-icon {
    font-size: 4rem;
    color: #e2e8f0;
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 0.5rem 0;
}

.empty-state p {
    color: #718096;
    margin: 0 0 2rem 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

@media (max-width: 768px) {
    .category-header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .category-info {
        flex-direction: column;
        text-align: center;
    }
    
    .category-details h1 {
        font-size: 2rem;
    }
    
    .plans-grid {
        grid-template-columns: 1fr;
    }
    
    .category-meta {
        justify-content: center;
    }
}
</style> 