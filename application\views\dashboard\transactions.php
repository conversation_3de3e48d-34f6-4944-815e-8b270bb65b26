<!-- Transactions Section -->
<div class="dashboard-welcome">
    <h1>Transaction History</h1>
    <a href="#" class="btn btn-primary">
        <i class="fas fa-plus"></i> Top Up Balance
    </a>
</div>

<!-- Transactions List -->
<div class="dashboard-section">
    <div class="section-header">
        <h5>All Transactions</h5>
    </div>
    <div class="table-responsive">
        <table class="dashboard-table">
            <thead>
                <tr>
                    <th>Transaction ID</th>
                    <th>Date & Time</th>
                    <th>Type</th>
                    <th>Amount</th>
                    <th>Payment Method</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($transactions)): ?>
                <tr>
                    <td colspan="7">
                        <div class="empty-state">
                            <i class="fas fa-money-bill-wave"></i>
                            <div>No transactions found</div>
                            <a href="#" class="btn btn-primary" style="margin-top: 1rem;">
                                <i class="fas fa-plus"></i> Make Your First Transaction
                            </a>
                        </div>
                    </td>
                </tr>
                <?php else: ?>
                    <?php foreach ($transactions as $transaction): ?>
                    <tr>
                        <td>
                            <div style="font-family: 'JetBrains Mono', monospace; color: #0ea5e9; font-weight: 600;">
                                #<?= $transaction->transaction_id ?>
                            </div>
                        </td>
                        <td>
                            <div style="color: white; font-weight: 500;"><?= date('d M Y', strtotime($transaction->created_at)) ?></div>
                            <div style="font-size: 0.875rem; color: rgba(255, 255, 255, 0.6);"><?= date('H:i', strtotime($transaction->created_at)) ?></div>
                        </td>
                        <td>
                            <span class="badge <?= $transaction->type == 'topup' ? 'success' : ($transaction->type == 'purchase' ? 'primary' : 'info') ?>">
                                <?= ucfirst($transaction->type) ?>
                            </span>
                        </td>
                        <td>
                            <div style="font-weight: 600; color: <?= $transaction->type == 'topup' ? '#22c55e' : '#ef4444' ?>;">
                                <?= $transaction->type == 'topup' ? '+' : '-' ?>Rp <?= number_format($transaction->amount, 0, ',', '.') ?>
                            </div>
                        </td>
                        <td>
                            <div style="color: rgba(255, 255, 255, 0.8);">
                                <?= ucfirst($transaction->payment_method) ?>
                            </div>
                        </td>
                        <td>
                            <span class="badge <?= $transaction->status == 'success' ? 'success' : ($transaction->status == 'pending' ? 'warning' : 'danger') ?>">
                                <?= ucfirst($transaction->status) ?>
                            </span>
                        </td>
                        <td>
                            <div style="display: flex; gap: 0.5rem;">
                                <a href="#" class="btn btn-primary btn-sm" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <?php if ($transaction->status == 'success'): ?>
                                <a href="#" class="btn btn-secondary btn-sm" title="Download Invoice" style="background: rgba(255, 255, 255, 0.1);">
                                    <i class="fas fa-download"></i>
                                </a>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Transaction Summary -->
<div class="dashboard-section">
    <div class="section-header">
        <h5>Transaction Summary</h5>
    </div>
    <div style="padding: 2rem;">
        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
            <div style="flex: 1; min-width: 200px;">
                <div style="display: flex; align-items: center; padding: 1rem 1.25rem; background: rgba(34, 197, 94, 0.08); border: 1px solid rgba(34, 197, 94, 0.15); border-radius: 12px; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 15px rgba(34, 197, 94, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                    <div style="color: #22c55e; font-size: 1.5rem; margin-right: 1rem;">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <div>
                        <div style="color: white; font-size: 1.1rem; font-weight: 600; margin-bottom: 0.25rem;">
                            Rp <?= number_format(array_sum(array_column(array_filter($transactions, function($t) { return $t->type == 'topup' && $t->status == 'success'; }), 'amount')), 0, ',', '.') ?>
                        </div>
                        <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.8rem;">Total Top Up</div>
                    </div>
                </div>
            </div>
            <div style="flex: 1; min-width: 200px;">
                <div style="display: flex; align-items: center; padding: 1rem 1.25rem; background: rgba(239, 68, 68, 0.08); border: 1px solid rgba(239, 68, 68, 0.15); border-radius: 12px; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 15px rgba(239, 68, 68, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                    <div style="color: #ef4444; font-size: 1.5rem; margin-right: 1rem;">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    <div>
                        <div style="color: white; font-size: 1.1rem; font-weight: 600; margin-bottom: 0.25rem;">
                            Rp <?= number_format(array_sum(array_column(array_filter($transactions, function($t) { return in_array($t->type, ['purchase', 'renewal']) && $t->status == 'success'; }), 'amount')), 0, ',', '.') ?>
                        </div>
                        <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.8rem;">Total Spent</div>
                    </div>
                </div>
            </div>
            <div style="flex: 1; min-width: 200px;">
                <div style="display: flex; align-items: center; padding: 1rem 1.25rem; background: rgba(14, 165, 233, 0.08); border: 1px solid rgba(14, 165, 233, 0.15); border-radius: 12px; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 15px rgba(14, 165, 233, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                    <div style="color: #0ea5e9; font-size: 1.5rem; margin-right: 1rem;">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <div>
                        <div style="color: white; font-size: 1.1rem; font-weight: 600; margin-bottom: 0.25rem;">
                            <?= count($transactions) ?>
                        </div>
                        <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.8rem;">Total Transactions</div>
                    </div>
                </div>
            </div>
            <div style="flex: 1; min-width: 200px;">
                <div style="display: flex; align-items: center; padding: 1rem 1.25rem; background: rgba(245, 158, 11, 0.08); border: 1px solid rgba(245, 158, 11, 0.15); border-radius: 12px; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 15px rgba(245, 158, 11, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                    <div style="color: #f59e0b; font-size: 1.5rem; margin-right: 1rem;">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div>
                        <div style="color: white; font-size: 1.1rem; font-weight: 600; margin-bottom: 0.25rem;">
                            <?= count(array_filter($transactions, function($t) { return $t->status == 'pending'; })) ?>
                        </div>
                        <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.8rem;">Pending</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 