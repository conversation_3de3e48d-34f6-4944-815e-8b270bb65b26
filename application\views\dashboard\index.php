<?php if ($this->session->flashdata('success')): ?>
    <div class="alert alert-success">
        <?= $this->session->flashdata('success') ?>
    </div>
<?php endif; ?>

<?php if ($this->session->flashdata('error')): ?>
    <div class="alert alert-danger">
        <?= $this->session->flashdata('error') ?>
    </div>
<?php endif; ?>

<!-- Welcome Section -->
<div class="dashboard-welcome">
    <h1 id="welcomeText">Welcome back, <?= $user->full_name ?>!</h1>
    <a href="<?= base_url('dashboard/servers') ?>" class="btn btn-primary">
        <i class="fas fa-plus"></i> Order New Server
    </a>
</div>

<!-- Stats Cards -->
<div class="dashboard-stats">
    <div class="stat-card">
        <div class="stat-card-header">
            <div class="stat-icon primary">
                <i class="fas fa-server"></i>
            </div>
            <div class="stat-info">
                <h6>Active Servers</h6>
                <h3><span class="counter" data-target="<?= count($servers) ?>">0</span></h3>
            </div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-card-header">
            <div class="stat-icon success">
                <i class="fas fa-wallet"></i>
            </div>
            <div class="stat-info">
                <h6>Balance</h6>
                <h3>Rp <span class="counter" data-target="<?= $user->balance ?>">0</span></h3>
            </div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-card-header">
            <div class="stat-icon info">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stat-info">
                <h6>Transactions</h6>
                <h3><span class="counter" data-target="<?= isset($transactions) ? count($transactions) : 0 ?>">0</span></h3>
            </div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-card-header">
            <div class="stat-icon warning">
                <i class="fas fa-ticket-alt"></i>
            </div>
            <div class="stat-info">
                <h6>Open Tickets</h6>
                <h3><span class="counter" data-target="<?= isset($tickets) ? count(array_filter($tickets, function($t) { return $t->status == 'open'; })) : 0 ?>">0</span></h3>
            </div>
        </div>
    </div>
</div>

<!-- Recent Servers -->
<div class="dashboard-section">
    <div class="section-header">
        <h5>Your Servers</h5>
        <a href="<?= base_url('dashboard/servers') ?>" class="section-link">View All</a>
    </div>
    <div class="table-responsive">
        <table class="dashboard-table">
            <thead>
                <tr>
                    <th>Server Name</th>
                    <th>Type</th>
                    <th>IP:Port</th>
                    <th>Status</th>
                    <th>Expiry Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!isset($servers) || empty($servers)): ?>
                <tr>
                    <td colspan="6">
                        <div class="empty-state">
                            <i class="fas fa-server"></i>
                            <div>You don't have any servers yet</div>
                            <a href="<?= base_url('dashboard/servers') ?>" class="btn btn-primary" style="margin-top: 1rem;">
                                <i class="fas fa-plus"></i> Order Your First Server
                            </a>
                        </div>
                    </td>
                </tr>
                <?php else: ?>
                    <?php foreach (array_slice($servers ?? [], 0, 5) as $server): ?>
                    <tr>
                        <td><?= $server->server_name ?></td>
                        <td>
                            <span class="badge <?= isset($server->category_slug) && $server->category_slug == 'fivem' ? 'primary' : (isset($server->category_slug) && $server->category_slug == 'samp' ? 'success' : 'danger') ?>">
                                <?= isset($server->category_name) ? strtoupper($server->category_name) : 'SERVER' ?>
                            </span>
                        </td>
                        <td><?= isset($server->server_ip) && isset($server->server_port) ? $server->server_ip . ':' . $server->server_port : 'Not Set' ?></td>
                        <td>
                            <span class="badge <?= $server->status == 'active' ? 'success' : ($server->status == 'suspended' ? 'warning' : 'danger') ?>">
                                <?= ucfirst($server->status) ?>
                            </span>
                        </td>
                        <td><?= isset($server->next_due_date) ? date('d M Y', strtotime($server->next_due_date)) : 'Not Set' ?></td>
                        <td>
                            <a href="#" class="btn btn-primary btn-sm">
                                <i class="fas fa-cog"></i>
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Recent Transactions -->
<div class="dashboard-section">
    <div class="section-header">
        <h5>Recent Transactions</h5>
        <a href="<?= base_url('dashboard/transactions') ?>" class="section-link">View All</a>
    </div>
    <div class="table-responsive">
        <table class="dashboard-table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Type</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th>Payment Method</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!isset($transactions) || empty($transactions)): ?>
                <tr>
                    <td colspan="5">
                        <div class="empty-state">
                            <i class="fas fa-money-bill-wave"></i>
                            <div>No transactions found</div>
                        </div>
                    </td>
                </tr>
                <?php else: ?>
                    <?php foreach (array_slice($transactions ?? [], 0, 5) as $transaction): ?>
                    <tr>
                        <td><?= date('d M Y H:i', strtotime($transaction->created_at)) ?></td>
                        <td>
                            <span class="badge <?= $transaction->type == 'topup' ? 'success' : 'primary' ?>">
                                <?= ucfirst($transaction->type) ?>
                            </span>
                        </td>
                        <td><?= format_currency($transaction->amount) ?></td>
                        <td>
                            <span class="badge <?= $transaction->status == 'success' ? 'success' : ($transaction->status == 'pending' ? 'warning' : 'danger') ?>">
                                <?= ucfirst($transaction->status) ?>
                            </span>
                        </td>
                        <td><?= $transaction->payment_method ?></td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Support Tickets -->
<div class="dashboard-section">
    <div class="section-header">
        <h5>Recent Support Tickets</h5>
        <a href="<?= base_url('dashboard/tickets') ?>" class="section-link">View All</a>
    </div>
    <div class="table-responsive">
        <table class="dashboard-table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Subject</th>
                    <th>Priority</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!isset($tickets) || empty($tickets)): ?>
                <tr>
                    <td colspan="5">
                        <div class="empty-state">
                            <i class="fas fa-ticket-alt"></i>
                            <div>No support tickets found</div>
                            <a href="<?= base_url('dashboard/tickets') ?>" class="btn btn-primary" style="margin-top: 1rem;">
                                <i class="fas fa-plus"></i> Create New Ticket
                            </a>
                        </div>
                    </td>
                </tr>
                <?php else: ?>
                    <?php foreach (array_slice($tickets ?? [], 0, 5) as $ticket): ?>
                    <tr>
                        <td><?= date('d M Y H:i', strtotime($ticket->created_at)) ?></td>
                        <td><?= $ticket->subject ?></td>
                        <td>
                            <span class="badge <?= $ticket->priority == 'high' ? 'danger' : ($ticket->priority == 'medium' ? 'warning' : 'info') ?>">
                                <?= ucfirst($ticket->priority) ?>
                            </span>
                        </td>
                        <td>
                            <span class="badge <?= $ticket->status == 'open' ? 'success' : ($ticket->status == 'answered' ? 'info' : 'secondary') ?>">
                                <?= ucfirst($ticket->status) ?>
                            </span>
                        </td>
                        <td>
                            <a href="#" class="btn btn-primary btn-sm">
                                <i class="fas fa-eye"></i>
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div> 