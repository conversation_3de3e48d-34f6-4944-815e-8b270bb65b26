<?php if ($this->session->flashdata('success')): ?>
    <div class="alert alert-success">
        <?= $this->session->flashdata('success') ?>
    </div>
<?php endif; ?>

<?php if ($this->session->flashdata('error')): ?>
    <div class="alert alert-danger">
        <?= $this->session->flashdata('error') ?>
    </div>
<?php endif; ?>

<!-- Profile Section -->
<div class="dashboard-welcome">
    <h1>My Profile</h1>
    <a href="<?= base_url('dashboard/change_password') ?>" class="btn btn-primary">
        <i class="fas fa-key"></i> Change Password
    </a>
</div>

<!-- Profile Form -->
<div class="dashboard-section">
    <div class="section-header">
        <h5>Profile Information</h5>
    </div>
    <div style="display: flex; justify-content: center; padding: 1.5rem;">
        <div style="width: 100%; max-width: 500px;">
            <form method="POST" action="<?= base_url('dashboard/profile') ?>">
                <div style="margin-bottom: 1.25rem;">
                    <label for="username" style="color: rgba(255, 255, 255, 0.8); margin-bottom: 0.5rem; display: block; font-weight: 500; font-size: 0.9rem;">Username</label>
                    <input type="text" id="username" value="<?= $user->username ?>" class="form-control" disabled style="background: rgba(255, 255, 255, 0.03); border: 1px solid rgba(255, 255, 255, 0.08); color: rgba(255, 255, 255, 0.5); padding: 0.625rem 0.875rem; border-radius: 6px; font-size: 0.875rem; width: 100%;">
                </div>
                
                <div style="margin-bottom: 1.25rem;">
                    <label for="email" style="color: rgba(255, 255, 255, 0.8); margin-bottom: 0.5rem; display: block; font-weight: 500; font-size: 0.9rem;">Email Address</label>
                    <input type="email" id="email" value="<?= $user->email ?>" class="form-control" disabled style="background: rgba(255, 255, 255, 0.03); border: 1px solid rgba(255, 255, 255, 0.08); color: rgba(255, 255, 255, 0.5); padding: 0.625rem 0.875rem; border-radius: 6px; font-size: 0.875rem; width: 100%;">
                </div>
                
                <div style="margin-bottom: 1.25rem;">
                    <label for="full_name" style="color: rgba(255, 255, 255, 0.8); margin-bottom: 0.5rem; display: block; font-weight: 500; font-size: 0.9rem;">Full Name <span style="color: #ef4444;">*</span></label>
                    <input type="text" id="full_name" name="full_name" value="<?= $user->full_name ?>" class="form-control" required style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.15); color: white; padding: 0.625rem 0.875rem; border-radius: 6px; font-size: 0.875rem; transition: all 0.3s ease; width: 100%;" onfocus="this.style.borderColor='#0ea5e9'; this.style.boxShadow='0 0 0 2px rgba(14, 165, 233, 0.1)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.15)'; this.style.boxShadow='none'">
                </div>
                
                <div style="margin-bottom: 1.25rem;">
                    <label for="phone" style="color: rgba(255, 255, 255, 0.8); margin-bottom: 0.5rem; display: block; font-weight: 500; font-size: 0.9rem;">Phone Number</label>
                    <input type="text" id="phone" name="phone" value="<?= $user->phone ?>" class="form-control" style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.15); color: white; padding: 0.625rem 0.875rem; border-radius: 6px; font-size: 0.875rem; transition: all 0.3s ease; width: 100%;" onfocus="this.style.borderColor='#0ea5e9'; this.style.boxShadow='0 0 0 2px rgba(14, 165, 233, 0.1)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.15)'; this.style.boxShadow='none'">
                </div>
                
                <div style="margin-bottom: 1.25rem;">
                    <label for="balance" style="color: rgba(255, 255, 255, 0.8); margin-bottom: 0.5rem; display: block; font-weight: 500; font-size: 0.9rem;">Current Balance</label>
                    <div style="background: rgba(34, 197, 94, 0.1); border: 1px solid rgba(34, 197, 94, 0.2); padding: 0.625rem 0.875rem; border-radius: 6px; color: #22c55e; font-weight: 600; font-size: 0.875rem;">
                        <i class="fas fa-wallet" style="margin-right: 0.5rem;"></i>
                        Rp <?= number_format($user->balance, 0, ',', '.') ?>
                    </div>
                </div>
                
                <div style="margin-bottom: 1.75rem;">
                    <label for="joined" style="color: rgba(255, 255, 255, 0.8); margin-bottom: 0.5rem; display: block; font-weight: 500; font-size: 0.9rem;">Member Since</label>
                    <div style="background: rgba(14, 165, 233, 0.1); border: 1px solid rgba(14, 165, 233, 0.2); padding: 0.625rem 0.875rem; border-radius: 6px; color: #0ea5e9; font-weight: 500; font-size: 0.875rem;">
                        <i class="fas fa-calendar" style="margin-right: 0.5rem;"></i>
                        <?= date('d M Y', strtotime($user->created_at)) ?>
                    </div>
                </div>
                
                <div style="padding-top: 1.25rem; border-top: 1px solid rgba(255, 255, 255, 0.1); text-align: center;">
                    <button type="submit" class="btn btn-primary" style="padding: 0.625rem 1.25rem; font-weight: 600; border-radius: 6px; font-size: 0.875rem;">
                        <i class="fas fa-save" style="margin-right: 0.5rem;"></i> Update Profile
                    </button>
                    <a href="<?= base_url('dashboard') ?>" class="btn" style="background: rgba(255, 255, 255, 0.08); color: rgba(255, 255, 255, 0.8); margin-left: 0.75rem; padding: 0.625rem 1.25rem; border-radius: 6px; text-decoration: none; transition: all 0.3s ease; font-size: 0.875rem;" onmouseover="this.style.background='rgba(255, 255, 255, 0.12)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.08)'">
                        <i class="fas fa-arrow-left" style="margin-right: 0.5rem;"></i> Back to Dashboard
                    </a>
                </div><br><br><br>
            </form>
        </div>
    </div>
</div> 