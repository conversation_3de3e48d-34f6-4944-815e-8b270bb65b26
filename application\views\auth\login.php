<?php
defined('BASEPATH') OR exit('No direct script access allowed');
// Load settings
if (!isset($GLOBALS['site_settings_loaded'])) {
    include_once VIEWPATH . 'includes/settings_loader.php';
}
?><!DOCTYPE html>
<html lang="id">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Login - <?= site_name() ?></title>
	<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
	<link href="<?php echo secure_asset('assets/css/style.css'); ?>" rel="stylesheet">
	<link href="<?php echo secure_asset('assets/css/auth.css'); ?>" rel="stylesheet">
</head>
<body>
	<!-- Navbar -->
	<?php $this->load->view('includes/header'); ?>

	<!-- Auth Section -->
	<section class="auth-section">
		<div class="auth-bg"></div>
		<div class="auth-container">
			<div class="auth-card">
				<div class="auth-header">
					<div class="auth-logo">
						<img src="<?php echo base_url(); ?>assets/images/logo.png" alt="<?= site_name() ?>">
					</div>
					<h1>Selamat Datang Kembali</h1>
					<p>Masuk ke akun <?= site_name() ?> Anda<br>untuk mengelola server gaming</p>
				</div>

				<?php if ($this->session->flashdata('success')): ?>
					<div class="alert alert-success">
						<?php echo $this->session->flashdata('success'); ?>
					</div>
				<?php endif; ?>

				<?php if (isset($error) && !empty($error)): ?>
					<div class="alert alert-danger">
						<?php echo $error; ?>
					</div>
				<?php endif; ?>

				<form class="auth-form" action="<?php echo base_url('auth/login'); ?>" method="POST">
					<div class="form-group">
						<label for="email">
							<i class="fas fa-envelope"></i>
							Email Address
						</label>
						<input type="email" id="email" name="email" required>
					</div>

					<div class="form-group">
						<label for="password">
							<i class="fas fa-lock"></i>
							Password
						</label>
						<div class="password-field">
							<input type="password" id="password" name="password" required>
							<button type="button" class="password-toggle" onclick="togglePassword()">
								<i class="fas fa-eye"></i>
							</button>
						</div>
					</div>

					<div class="form-options">
						<label class="checkbox-wrapper">
							<input type="checkbox" name="remember">
							<span class="checkmark"></span>
							Ingat saya
						</label>
						<!-- Hide forgot password until implemented
						<a href="#" class="forgot-link">Lupa password?</a>
						-->
					</div>

					<button type="submit" class="auth-btn">
						<i class="fas fa-sign-in-alt"></i>
						Masuk ke Dashboard
					</button>
				</form>
<!-- 
				<div class="auth-divider">
					<span>atau</span> -->
				</div>

                    <!-- <div class="social-login">
                        <button class="social-btn google">
                            <i class="fab fa-google"></i>
                            Masuk dengan Google
                        </button>
                        <button class="social-btn discord">
                            <i class="fab fa-discord"></i>
                            Masuk dengan Discord
                        </button>
                    </div> -->

				<div class="auth-footer">
					<p>Belum punya akun? <a href="<?php echo base_url('auth/register'); ?>">Daftar di sini</a></p>
				</div>
			</div>

			<div class="auth-info">
				<div class="info-card">
					<div class="info-icon">
						<i class="fas fa-bolt"></i>
					</div>
					<h3>Performa Enterprise</h3>
					<p>Server gaming dengan uptime 99.9% dan latency ultra-rendah untuk pengalaman gaming terbaik.</p>
				</div>
				<div class="info-card">
					<div class="info-icon">
						<i class="fas fa-shield-alt"></i>
					</div>
					<h3>Keamanan Maksimal</h3>
					<p>Perlindungan DDoS hingga 1Tbps dengan monitoring real-time 24/7 oleh tim expert.</p>
				</div>
				<div class="info-card">
					<div class="info-icon">
						<i class="fas fa-headset"></i>
					</div>
					<h3>Support Premium</h3>
					<p>Tim support gaming profesional siap membantu melalui live chat dan Discord setiap saat.</p>
				</div>
			</div>
		</div>
	</section>

	<script>
		function togglePassword() {
			const passwordField = document.getElementById('password');
			const toggleBtn = document.querySelector('.password-toggle i');
			
			if (passwordField.type === 'password') {
				passwordField.type = 'text';
				toggleBtn.className = 'fas fa-eye-slash';
			} else {
				passwordField.type = 'password';
				toggleBtn.className = 'fas fa-eye';
			}
		}

		// Add floating particles
		document.addEventListener('DOMContentLoaded', function() {
			createFloatingParticles();
		});

		function createFloatingParticles() {
			const authBg = document.querySelector('.auth-bg');
			const colors = ['#0EA5E9', '#A855F7', '#FF6B00'];
			
			for (let i = 0; i < 15; i++) {
				const particle = document.createElement('div');
				particle.className = 'floating-particle';
				particle.style.cssText = `
					position: absolute;
					width: ${Math.random() * 4 + 2}px;
					height: ${Math.random() * 4 + 2}px;
					background: ${colors[Math.floor(Math.random() * colors.length)]};
					border-radius: 50%;
					opacity: ${Math.random() * 0.3 + 0.1};
					animation: floatAuth ${Math.random() * 20 + 15}s linear infinite;
					left: ${Math.random() * 100}%;
					top: ${Math.random() * 100}%;
				`;
				authBg.appendChild(particle);
			}
		}
	</script>

	<style>
		@keyframes floatAuth {
			0% {
				transform: translateY(0) rotate(0deg);
				opacity: 0;
			}
			10% {
				opacity: 1;
			}
			90% {
				opacity: 1;
			}
			100% {
				transform: translateY(-50px) rotate(360deg);
				opacity: 0;
			}
		}
	</style>
</body>
</html> 