/*!
 * DopMiner Game Server Hosting - Custom Styles
 * Copyright (c) 2024 DopMiner - All Rights Reserved
 * 
 * NOTICE: This file is protected by copyright law.
 * Unauthorized copying, modification, or distribution is prohibited.
 * Contact <EMAIL> for licensing inquiries.
 * 
 * Protected by multiple security layers - Access monitored
 * Generated: 2024 | Version: 1.0.0 | Status: PROTECTED
 */

:root {
    --primary-blue: #0ea5e9;
    --secondary-blue: #3b82f6;
    --dark-blue: #1e40af;
    --light-blue: #e0f2fe;
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    --black: #000000;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: 
        linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%),
        radial-gradient(circle at 25% 25%, #FF6B00 0%, transparent 30%),
        radial-gradient(circle at 75% 75%, #DC2626 0%, transparent 25%),
        radial-gradient(circle at 50% 10%, #0EA5E9 0%, transparent 20%),
        linear-gradient(45deg, #1E293B 0%, #334155 50%, #475569 100%);
    background-size: 100% 100%, 800px 800px, 600px 600px, 400px 400px, 100% 100%;
    background-attachment: fixed;
    color: var(--white);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Navbar */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(15, 23, 42, 0.85);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
    animation: fadeInDown 0.5s ease-out forwards;
}

.navbar.scrolled {
    background: rgba(15, 23, 42, 0.95);
    border-bottom-color: rgba(59, 130, 246, 0.2);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.logo {
    font-family: 'JetBrains Mono', monospace;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-blue);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
}

.logo:hover {
    color: var(--secondary-blue);
    transform: translateY(-1px) scale(1.02);
}

.logo-img {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.logo:hover .logo-img {
    transform: rotate(5deg) scale(1.05);
    box-shadow: 0 4px 16px rgba(14, 165, 233, 0.4);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
    align-items: center;
}

.nav-menu li {
    position: relative;
}

.nav-menu a {
    color: var(--gray-300);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    position: relative;
    /* padding: 0.5rem 0; */
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-menu a:hover {
    color: var(--white);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background: var(--primary-blue);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

/* Dropdown Menu */
.dropdown {
    position: relative;
}

.dropdown-toggle i {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.dropdown:hover .dropdown-toggle i {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--gray-900);
    border: 1px solid var(--gray-700);
    border-radius: 8px;
    min-width: 200px;
    padding: 0.5rem 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    z-index: 1001;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    list-style: none;
}

.dropdown-menu a {
    display: block;
    padding: 0.75rem 1.5rem;
    color: var(--gray-300);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border-bottom: none;
}

.dropdown-menu a::after {
    display: none;
}

.dropdown-menu a:hover {
    background: var(--gray-800);
    color: var(--primary-blue);
    padding-left: 2rem;
}

.dropdown-divider {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 0.5rem 0;
    border: none;
    width: 100%;
    display: block;
}

.text-danger {
    color: #ef4444 !important;
}

.text-danger:hover {
    color: #dc2626 !important;
}

.nav-cta {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%) !important;
    color: var(--white) !important;
    padding: 0.5rem 1.25rem;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(14, 165, 233, 0.2);
    border: 1px solid rgba(14, 165, 233, 0.3);
    display: flex;
    align-items: center;
    gap: 0.4rem;
    min-width: auto;
}

.nav-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    transition: left 0.5s ease;
}

.nav-cta:hover {
    background: linear-gradient(135deg, var(--secondary-blue) 0%, #0284c7 100%) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
    border-color: rgba(14, 165, 233, 0.5);
}

.nav-cta:hover::before {
    left: 100%;
}

.nav-cta::after {
    display: none;
}

.nav-cta i {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.nav-cta:hover i {
    transform: scale(1.1);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
}

.nav-toggle span {
    width: 25px;
    height: 2px;
    background: var(--white);
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding-top: 100px;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 50%, rgba(255, 107, 0, 0.12) 0%, transparent 45%),
        radial-gradient(circle at 80% 20%, rgba(220, 38, 38, 0.10) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(14, 165, 233, 0.15) 0%, transparent 40%),
        radial-gradient(circle at 60% 30%, rgba(168, 85, 247, 0.06) 0%, transparent 55%);
    z-index: 1;
}

.hero-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(rgba(255, 107, 0, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(220, 38, 38, 0.04) 1px, transparent 1px),
        linear-gradient(45deg, rgba(14, 165, 233, 0.03) 1px, transparent 1px),
        radial-gradient(circle at 50% 50%, rgba(168, 85, 247, 0.02) 2px, transparent 2px);
    background-size: 80px 80px, 60px 60px, 120px 120px, 40px 40px;
    z-index: 1;
    animation: gridMove 50s linear infinite;
}

@keyframes gridMove {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(40px, 40px);
    }
}

.hero-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1.2fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-content {
    max-width: 600px;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(14, 165, 233, 0.1);
    border: 1px solid rgba(14, 165, 233, 0.2);
    color: var(--primary-blue);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, var(--white) 0%, var(--gray-300) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-content .highlight {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-content p {
    font-size: 1.25rem;
    color: var(--gray-400);
    margin-bottom: 2.5rem;
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    box-shadow: 0 4px 15px rgba(14, 165, 233, 0.25);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(14, 165, 233, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--gray-600);
}

.btn-secondary:hover {
    border-color: var(--primary-blue);
    color: var(--primary-blue);
    transform: translateY(-2px);
}

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.server-showcase {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
    max-width: 320px;
    margin: 0 auto;
}

.server-card {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(15, 23, 42, 0.95) 100%);
    border: 1px solid var(--gray-700);
    border-radius: 12px;
    padding: 1.25rem;
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    width: 100%;
    opacity: 0.8;
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

.server-card.active {
    border-color: var(--primary-blue);
    box-shadow: 0 12px 40px rgba(14, 165, 233, 0.2);
    transform: translateY(-3px);
    opacity: 1;
}

.server-card:nth-child(1) {
    order: 1;
    animation-delay: 0.2s;
}

.server-card:nth-child(2) {
    order: 2;
    animation-delay: 0.4s;
}

.server-card:nth-child(3) {
    order: 3;
    animation-delay: 0.6s;
}

.server-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-blue);
    box-shadow: 0 16px 32px rgba(14, 165, 233, 0.2);
    opacity: 1;
}

.server-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.server-icon {
    width: 32px;
    height: 32px;
    background: var(--white);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 0.85rem;
    flex-shrink: 0;
    padding: 4px;
    border: 1px solid var(--gray-600);
}

.server-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 4px;
}

.server-info {
    flex: 1;
    min-width: 0;
}

.server-info h3 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.15rem;
    color: var(--white);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.server-info p {
    font-size: 0.75rem;
    color: var(--gray-400);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.server-stats {
    display: flex;
    gap: 0.5rem;
    justify-content: space-between;
}

.server-stat {
    text-align: center;
    flex: 1;
    min-width: 0;
}

.server-stat .stat-value {
    font-size: 1rem;
    font-weight: 700;
    color: var(--primary-blue);
    display: block;
    margin-bottom: 0.15rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.server-stat .stat-label {
    font-size: 0.6rem;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.3px;
    line-height: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Protection Section */
.protection {
    padding: 2rem 0;
    background: 
        linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.98) 100%),
        radial-gradient(circle at 90% 10%, rgba(14, 165, 233, 0.12) 0%, transparent 50%),
        radial-gradient(circle at 10% 90%, rgba(168, 85, 247, 0.08) 0%, transparent 40%);
    background-attachment: fixed;
    position: relative;
    overflow: hidden;
}

.protection::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: 
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 120px,
            rgba(255, 107, 0, 0.015) 120px,
            rgba(255, 107, 0, 0.015) 122px
        );
    animation: floatingPattern 60s linear infinite;
    z-index: 1;
}

.protection-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 2;
}

.protection-content {
    display: grid;
    grid-template-columns: 1.2fr 1.3fr;
    gap: 4rem;
    align-items: center;
}

.protection-left {
    display: flex;
    justify-content: center;
    align-items: center;
}

.protection-shield {
    position: relative;
    width: 280px;
    height: 280px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.shield-icon {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 4;
    position: relative;
    box-shadow: 0 15px 40px rgba(14, 165, 233, 0.4);
}

.shield-icon i {
    font-size: 3rem;
    color: var(--white);
}

.shield-rings {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.ring {
    position: absolute;
    border: 2px solid rgba(14, 165, 233, 0.3);
    border-radius: 50%;
    animation: ringPulse 3s ease-in-out infinite;
}

.ring-1 {
    width: 150px;
    height: 150px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 0s;
}

.ring-2 {
    width: 200px;
    height: 200px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 1s;
}

.ring-3 {
    width: 250px;
    height: 250px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 2s;
}

.ring-4 {
    width: 280px;
    height: 280px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 3s;
    border-color: rgba(14, 165, 233, 0.15);
}

@keyframes ringPulse {
    0% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(0.8);
    }
    50% {
        opacity: 0.5;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(1.2);
    }
}

.protection-right h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 1.5rem;
}

.protection-description {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--gray-400);
    margin-bottom: 2.5rem;
    max-width: 600px;
}

.protection-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 3rem;
}

.feature-indicator {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: rgba(30, 41, 59, 0.5);
    border: 1px solid rgba(14, 165, 233, 0.2);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.feature-indicator i {
    color: var(--primary-blue);
    font-size: 1.1rem;
}

.feature-indicator span {
    color: var(--gray-300);
    font-size: 0.9rem;
    font-weight: 500;
}

.attack-notifications {
    background: transparent;
    border: none;
    border-radius: 0;
    padding: 0;
    backdrop-filter: none;
    height: 260px;
    overflow: hidden;
    position: relative;
}

.attack-notification {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.25rem;
    background: rgba(15, 23, 42, 0.95);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    animation: slideInUp 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: absolute;
    left: 0;
    right: 0;
    height: 70px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transition: top 0.6s cubic-bezier(0.4, 0, 0.2, 1), transform 0.3s ease, opacity 0.3s ease;
}

.attack-notification::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #ef4444;
}

.attack-notification.mitigated::before {
    background: #10b981;
}

.attack-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.attack-icon {
    width: 32px;
    height: 32px;
    background: #ef4444;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
}

.attack-details h4 {
    color: var(--white);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.attack-details p {
    color: var(--gray-400);
    font-size: 0.75rem;
}

.attack-status {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-mitigating {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
    border: 1px solid rgba(251, 191, 36, 0.3);
}

.status-mitigated {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

@keyframes slideInUp {
    0% {
        transform: translateY(30px) scale(0.95);
        opacity: 0;
    }
    50% {
        transform: translateY(-5px) scale(0.98);
        opacity: 0.7;
    }
    100% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

@keyframes slideOutUp {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    50% {
        transform: translateY(-10px) scale(0.98);
        opacity: 0.5;
    }
    100% {
        transform: translateY(-30px) scale(0.90);
        opacity: 0;
    }
}

.attack-notification.removing {
    animation: slideOutUp 0.8s cubic-bezier(0.55, 0.055, 0.675, 0.19) forwards;
}

@keyframes floatingPattern {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Features Section */
.features {
    padding: 8rem 0;
    background: 
        linear-gradient(135deg, rgba(30, 41, 59, 0.92) 0%, rgba(51, 65, 85, 0.90) 100%),
        radial-gradient(circle at 15% 85%, rgba(255, 107, 0, 0.12) 0%, transparent 50%),
        radial-gradient(circle at 85% 15%, rgba(220, 38, 38, 0.10) 0%, transparent 45%),
        radial-gradient(circle at 50% 50%, rgba(14, 165, 233, 0.08) 0%, transparent 60%),
        url('../images/mengapa.png');
    background-attachment: fixed, fixed, fixed, fixed, scroll;
    background-size: auto, auto, auto, auto, cover;
    background-position: center, center, center, center, center;
    background-repeat: no-repeat, no-repeat, no-repeat, no-repeat, no-repeat;
    position: relative;
    overflow: hidden;
}

.features::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        repeating-linear-gradient(
            90deg,
            transparent,
            transparent 150px,
            rgba(14, 165, 233, 0.02) 150px,
            rgba(14, 165, 233, 0.02) 152px
        ),
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 200px,
            rgba(255, 107, 0, 0.015) 200px,
            rgba(255, 107, 0, 0.015) 202px
        );
    z-index: 1;
    animation: featuresPattern 40s linear infinite;
}

.features::after {
    content: '';
    position: absolute;
    top: -100px;
    left: -100px;
    right: -100px;
    bottom: -100px;
    background: 
        radial-gradient(circle at 25% 25%, rgba(168, 85, 247, 0.06) 0%, transparent 30%),
        radial-gradient(circle at 75% 75%, rgba(34, 197, 94, 0.04) 0%, transparent 25%),
        radial-gradient(circle at 80% 30%, rgba(249, 115, 22, 0.05) 0%, transparent 35%);
    z-index: 1;
    animation: ambientGlow 35s ease-in-out infinite alternate;
}

@keyframes featuresPattern {
    0% {
        transform: translateX(0) translateY(0);
    }
    100% {
        transform: translateX(50px) translateY(25px);
    }
}

@keyframes ambientGlow {
    0% {
        opacity: 0.7;
        transform: scale(1) rotate(0deg);
    }
    100% {
        opacity: 1;
        transform: scale(1.1) rotate(5deg);
    }
}

@keyframes cardGlow {
    0% {
        opacity: 0.3;
        transform: rotate(0deg) scale(1);
    }
    100% {
        opacity: 0.6;
        transform: rotate(5deg) scale(1.1);
    }
}

/* Features section decorative elements */
.features .particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.features .particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(14, 165, 233, 0.4);
    border-radius: 50%;
    animation: floatParticle 15s infinite linear;
}

.features .particle:nth-child(1) { left: 10%; animation-delay: 0s; animation-duration: 20s; }
.features .particle:nth-child(2) { left: 20%; animation-delay: 2s; animation-duration: 18s; background: rgba(168, 85, 247, 0.3); }
.features .particle:nth-child(3) { left: 30%; animation-delay: 4s; animation-duration: 22s; }
.features .particle:nth-child(4) { left: 40%; animation-delay: 6s; animation-duration: 16s; background: rgba(34, 197, 94, 0.3); }
.features .particle:nth-child(5) { left: 50%; animation-delay: 8s; animation-duration: 24s; }
.features .particle:nth-child(6) { left: 60%; animation-delay: 1s; animation-duration: 19s; background: rgba(255, 107, 0, 0.3); }
.features .particle:nth-child(7) { left: 70%; animation-delay: 3s; animation-duration: 21s; }
.features .particle:nth-child(8) { left: 80%; animation-delay: 5s; animation-duration: 17s; background: rgba(220, 38, 38, 0.3); }
.features .particle:nth-child(9) { left: 85%; animation-delay: 7s; animation-duration: 23s; }
.features .particle:nth-child(10) { left: 95%; animation-delay: 9s; animation-duration: 15s; background: rgba(168, 85, 247, 0.4); }

@keyframes floatParticle {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

.features-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 2;
}

.section-header {
    text-align: center;
    margin-bottom: 5rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--white);
}

.section-header p {
    font-size: 1.2rem;
    color: var(--gray-400);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: 
        linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.85) 100%),
        radial-gradient(circle at bottom right, rgba(14, 165, 233, 0.05) 0%, transparent 70%);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 16px;
    padding: 2.5rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 
        0 10px 25px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-blue), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: 
        radial-gradient(circle, rgba(14, 165, 233, 0.03) 0%, transparent 70%),
        radial-gradient(circle at 30% 70%, rgba(168, 85, 247, 0.02) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.4s ease;
    pointer-events: none;
}

.feature-card:hover {
    transform: translateY(-12px) scale(1.02);
    border-color: rgba(14, 165, 233, 0.4);
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.25),
        0 10px 30px rgba(14, 165, 233, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    background: 
        linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 100%),
        radial-gradient(circle at bottom right, rgba(14, 165, 233, 0.08) 0%, transparent 70%);
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-card:hover::after {
    opacity: 1;
    animation: cardGlow 2s ease-in-out infinite alternate;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.feature-icon i {
    font-size: 1.5rem;
    color: var(--white);
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--white);
}

.feature-card p {
    color: var(--gray-400);
    line-height: 1.6;
}

/* Footer */
.footer {
    background: var(--black);
    padding: 4rem 0 2rem;
    border-top: 1px solid var(--gray-800);
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1.5fr repeat(3, 1fr);
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-brand h3 {
    font-family: 'JetBrains Mono', monospace;
    font-size: 1.5rem;
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.footer-brand p {
    color: var(--gray-400);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--gray-800);
    border: 1px solid var(--gray-700);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
    color: var(--white);
    transform: translateY(-2px);
}

.footer-section h4 {
    color: var(--white);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.footer-section a {
    display: block;
    color: var(--gray-400);
    text-decoration: none;
    margin-bottom: 0.75rem;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--primary-blue);
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--gray-500);
    font-size: 0.9rem;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: var(--gray-500);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-blue);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-content {
        max-width: none;
    }

    .server-showcase {
        max-width: 280px;
        gap: 0.75rem;
    }

    .server-card {
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 100%;
        flex-direction: column;
        background: var(--gray-900);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        padding: 2rem 0;
        border-top: 1px solid var(--gray-700);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu .dropdown-menu {
        position: static;
        background: var(--gray-800);
        border: none;
        box-shadow: none;
        opacity: 1;
        visibility: visible;
        transform: none;
        display: none;
        margin-top: 0.5rem;
    }

    .nav-menu .dropdown.active .dropdown-menu {
        display: block;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-cta {
        margin-top: 1rem;
        padding: 0.7rem 1.5rem;
        justify-content: center;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
    }

    .protection-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }
    
    .protection-features {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .attack-notifications {
        height: 220px;
    }
    
    .attack-notification {
        padding: 0.875rem 1rem;
        height: 65px;
    }
    
    .attack-details h4 {
        font-size: 0.85rem;
    }
    
    .attack-details p {
        font-size: 0.7rem;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 1rem;
    }

    .hero-container {
        padding: 0 1rem;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .btn {
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
    }

    .protection-right h2 {
        font-size: 2rem;
    }
    
    .protection-shield {
        width: 200px;
        height: 200px;
    }
    
    .shield-icon {
        width: 80px;
        height: 80px;
        border-radius: 16px;
    }
    
    .shield-icon i {
        font-size: 2.5rem;
    }
    
    .protection-right h2 {
        font-size: 2rem;
    }
    
    .protection-description {
        font-size: 1rem;
        margin-bottom: 2rem;
    }
    
    .ring-1 {
        width: 120px;
        height: 120px;
    }
    
    .ring-2 {
        width: 150px;
        height: 150px;
    }
    
    .ring-3 {
        width: 180px;
        height: 180px;
    }
    
    .ring-4 {
        width: 200px;
        height: 200px;
    }
    
    .attack-notification {
        padding: 0.75rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        text-align: left;
    }
    
    .attack-info {
        width: 100%;
    }
    
    .attack-status {
        align-self: flex-end;
    }

    .server-showcase {
        max-width: 260px;
        gap: 0.5rem;
    }

    .server-card {
        padding: 0.875rem;
    }

    .server-stat .stat-value {
        font-size: 0.9rem;
    }

    .server-stat .stat-label {
        font-size: 0.55rem;
    }

    .server-info h3 {
        font-size: 0.8rem;
    }

    .server-info p {
        font-size: 0.7rem;
    }

    .server-icon {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }
}

/* Animasi untuk konten */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-content {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

/* Animasi untuk server cards */
.server-card {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

.server-card:nth-child(1) { animation-delay: 0.2s; }
.server-card:nth-child(2) { animation-delay: 0.4s; }
.server-card:nth-child(3) { animation-delay: 0.6s; }

/* Animasi untuk fitur cards */
.feature-card {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

.feature-card:nth-child(1) { animation-delay: 0.3s; }
.feature-card:nth-child(2) { animation-delay: 0.5s; }
.feature-card:nth-child(3) { animation-delay: 0.7s; }

/* Animasi untuk DDoS Protection */
.ddos-protection {
    animation: fadeInUp 0.8s ease-out forwards;
    opacity: 0;
    animation-delay: 0.4s;
}

/* Animasi untuk navbar */
.navbar {
    animation: fadeInDown 0.5s ease-out forwards;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
} 