<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= html_escape($category->name) ?> - <?= site_name() ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <link href="<?php echo secure_asset('assets/css/style.css'); ?>" rel="stylesheet">
</head>
<body>
    <?php $this->load->view('includes/header', ['categories' => $categories]); ?>

    <section class="page-header">
        <div class="page-header-container">
            <h1><?= html_escape($category->name) ?></h1>
            <p>Pilih paket yang paling sesuai dengan kebutuhan Anda.</p>
        </div>
    </section>

    <section class="plans-section">
        <div class="plans-container">
            <?php if (!empty($plans)): ?>
                <div class="plans-grid">
                    <?php foreach ($plans as $plan): ?>
                        <div class="plan-card">
                            <div class="plan-header">
                                <h3><?= html_escape($plan->name) ?></h3>
                                <?php if (isset($plan->badge) && $plan->badge): ?>
                                    <span class="plan-badge"><?= html_escape($plan->badge) ?></span>
                                <?php endif; ?>
                            </div>
                            <div class="plan-price">
                                <span class="price">Rp <?= number_format($plan->price_monthly, 0, ',', '.') ?></span>
                                <span class="cycle">/ bulan</span>
                            </div>
                            <div class="plan-features">
                                <ul>
                                    <?php foreach ($plan->specifications as $spec): ?>
                                        <li><i class="fas fa-check-circle"></i> <?= html_escape($spec) ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <div class="plan-footer">
                                <a href="<?= base_url('order/plan/' . $plan->slug) ?>" class="btn btn-primary">Pesan Sekarang</a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="no-plans">
                    <p>Belum ada paket yang tersedia untuk kategori ini.</p>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <?php $this->load->view('includes/footer'); ?>

    <script src="<?php echo secure_asset('assets/js/main.js'); ?>"></script>
</body>
</html>