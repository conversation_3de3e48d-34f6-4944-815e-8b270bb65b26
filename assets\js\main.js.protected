// DopMiner - Main JavaScript File

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    initializeCounters();
    initializeScrollEffects();
    initializeSmoothScrolling();
});

// Navigation functionality
function initializeNavigation() {
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    const navbar = document.getElementById('navbar');

    // Mobile menu toggle
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            
            // Animate hamburger menu
            const spans = navToggle.querySelectorAll('span');
            if (navMenu.classList.contains('active')) {
                spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                spans[1].style.opacity = '0';
                spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
            } else {
                spans[0].style.transform = '';
                spans[1].style.opacity = '';
                spans[2].style.transform = '';
            }
        });
    }

    // Close mobile menu when clicking on a link
    document.querySelectorAll('.nav-menu a').forEach(link => {
        link.addEventListener('click', function() {
            if (navMenu) {
                navMenu.classList.remove('active');
                
                // Reset hamburger menu
                const spans = navToggle.querySelectorAll('span');
                spans[0].style.transform = '';
                spans[1].style.opacity = '';
                spans[2].style.transform = '';
            }
        });
    });

    // Mobile dropdown functionality
    if (window.innerWidth <= 768) {
        document.querySelectorAll('.dropdown-toggle').forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                const dropdown = this.closest('.dropdown');
                dropdown.classList.toggle('active');
            });
        });
    }

    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        if (navMenu && !navMenu.contains(e.target) && !navToggle.contains(e.target)) {
            navMenu.classList.remove('active');
            
            // Reset hamburger menu
            if (navToggle) {
                const spans = navToggle.querySelectorAll('span');
                spans[0].style.transform = '';
                spans[1].style.opacity = '';
                spans[2].style.transform = '';
            }
        }
    });
}

// Counter animation with intersection observer
function initializeCounters() {
    let countersAnimated = false; // Flag to prevent re-animation

    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && !countersAnimated) {
                countersAnimated = true; // Set flag to prevent re-animation
                
                const counters = entry.target.querySelectorAll('.stat-number');
                counters.forEach(counter => {
                    const target = parseFloat(counter.getAttribute('data-target'));
                    if (target > 0) {
                        animateCounter(counter, target);
                    }
                });
                
                // Unobserve to prevent re-triggering
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    const statsSection = document.querySelector('.stats');
    if (statsSection) {
        observer.observe(statsSection);
    }
}

// Counter animation function
function animateCounter(element, target) {
    let current = 0;
    const increment = target / 60; // 60 frames for smooth animation
    const isDecimal = target % 1 !== 0;
    
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            if (target === 10000) {
                element.textContent = '10,000+';
            } else if (target === 99.9) {
                element.textContent = '99.9%';
            } else if (target === 5) {
                element.textContent = '5ms';
            } else if (target === 24) {
                element.textContent = '24/7';
            } else {
                element.textContent = isDecimal ? target.toFixed(1) : Math.floor(target).toLocaleString();
            }
            clearInterval(timer);
        } else {
            if (isDecimal) {
                element.textContent = current.toFixed(1);
            } else {
                element.textContent = Math.floor(current).toLocaleString();
            }
        }
    }, 16); // ~60fps
}

// Scroll effects
function initializeScrollEffects() {
    const navbar = document.getElementById('navbar');
    
    let ticking = false;

    function updateNavbar() {
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
        ticking = false;
    }

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateNavbar);
            ticking = true;
        }
    }

    window.addEventListener('scroll', requestTick);
}

// Smooth scrolling for anchor links
function initializeSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const headerOffset = 80;
                const elementPosition = target.getBoundingClientRect().top;
                const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                window.scrollTo({
                    top: offsetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Parallax effect for hero section
function initializeParallax() {
    const heroGrid = document.querySelector('.hero-grid');
    
    if (heroGrid) {
        let ticking = false;

        function updateParallax() {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            heroGrid.style.transform = `translateY(${rate}px)`;
            ticking = false;
        }

        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateParallax);
                ticking = true;
            }
        }

        window.addEventListener('scroll', requestTick);
    }
}

// Server card cycling animation
function initializeServerCards() {
    const serverCards = document.querySelectorAll('.server-card');
    let currentCard = 0;

    if (serverCards.length > 0) {
        setInterval(() => {
            // Remove active class from all cards
            serverCards.forEach(card => card.classList.remove('active'));
            
            // Add active class to current card
            serverCards[currentCard].classList.add('active');
            
            // Move to next card
            currentCard = (currentCard + 1) % serverCards.length;
        }, 3000); // Change every 3 seconds
    }
}

// Animated latency display
function initializeLatencyAnimation() {
    const latencyElements = document.querySelectorAll('.server-stat .stat-label');
    const latencyValues = [];

    // Find all latency stat elements
    latencyElements.forEach(element => {
        if (element.textContent.trim().toLowerCase() === 'latency') {
            const valueElement = element.parentElement.querySelector('.stat-value');
            if (valueElement) {
                latencyValues.push(valueElement);
            }
        }
    });

    if (latencyValues.length === 0) return;

    // Function to generate random latency between 15-40ms
    function generateRandomLatency() {
        return Math.floor(Math.random() * (40 - 15 + 1)) + 15;
    }

    // Function to smoothly animate to new latency value
    function animateLatency(element, targetLatency) {
        const currentLatency = parseInt(element.textContent.replace('ms', ''));
        const difference = targetLatency - currentLatency;
        const steps = 10;
        const stepSize = difference / steps;
        let currentStep = 0;

        const animationInterval = setInterval(() => {
            currentStep++;
            const newValue = Math.round(currentLatency + (stepSize * currentStep));
            element.textContent = newValue + 'ms';

            if (currentStep >= steps) {
                element.textContent = targetLatency + 'ms';
                clearInterval(animationInterval);
            }
        }, 50); // 50ms per step for smooth animation
    }

    // Update latency every 2-4 seconds randomly
    function scheduleNextUpdate() {
        const delay = Math.random() * (4000 - 2000) + 2000; // Random delay between 2-4 seconds
        
        setTimeout(() => {
            latencyValues.forEach(element => {
                const newLatency = generateRandomLatency();
                animateLatency(element, newLatency);
            });
            
            scheduleNextUpdate(); // Schedule next update
        }, delay);
    }

    // Start the animation cycle
    scheduleNextUpdate();
}

// Initialize additional features when window loads
window.addEventListener('load', function() {
    initializeParallax();
    initializeServerCards();
    initializeLatencyAnimation();
});

// Handle window resize
window.addEventListener('resize', function() {
    // Reset mobile menu on resize
    const navMenu = document.getElementById('nav-menu');
    const navToggle = document.getElementById('nav-toggle');
    
    if (window.innerWidth > 768 && navMenu) {
        navMenu.classList.remove('active');
        
        if (navToggle) {
            const spans = navToggle.querySelectorAll('span');
            spans[0].style.transform = '';
            spans[1].style.opacity = '';
            spans[2].style.transform = '';
        }
    }
});

// Utility functions
const Utils = {
    // Throttle function for performance optimization
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    },

    // Debounce function for performance optimization
    debounce: function(func, wait, immediate) {
        let timeout;
        return function() {
            const context = this, args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    },

    // Check if element is in viewport
    isInViewport: function(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }
};

// Export for global access if needed
window.DopMiner = {
    Utils: Utils,
    initializeNavigation: initializeNavigation,
    initializeCounters: initializeCounters,
    initializeScrollEffects: initializeScrollEffects,
    initializeSmoothScrolling: initializeSmoothScrolling,
    initializeLatencyAnimation: initializeLatencyAnimation
}; 