<!-- Support Tickets Section -->
<div class="dashboard-welcome">
    <h1>Support Tickets</h1>
    <a href="#" class="btn btn-primary">
        <i class="fas fa-plus"></i> Create New Ticket
    </a>
</div>

<!-- Tickets List -->
<div class="dashboard-section">
    <div class="section-header">
        <h5>My Support Requests</h5>
    </div>
    <div class="table-responsive">
        <table class="dashboard-table">
            <thead>
                <tr>
                    <th>Ticket ID</th>
                    <th>Subject</th>
                    <th>Priority</th>
                    <th>Status</th>
                    <th>Created Date</th>
                    <th>Last Updated</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($tickets)): ?>
                <tr>
                    <td colspan="7">
                        <div class="empty-state">
                            <i class="fas fa-ticket-alt"></i>
                            <div>No support tickets found</div>
                            <a href="#" class="btn btn-primary" style="margin-top: 1rem;">
                                <i class="fas fa-plus"></i> Create Your First Ticket
                            </a>
                        </div>
                    </td>
                </tr>
                <?php else: ?>
                    <?php foreach ($tickets as $ticket): ?>
                    <tr>
                        <td>
                            <div style="font-family: 'JetBrains Mono', monospace; color: #0ea5e9; font-weight: 600;">
                                #<?= str_pad($ticket->id, 6, '0', STR_PAD_LEFT) ?>
                            </div>
                        </td>
                        <td>
                            <div style="color: white; font-weight: 500; margin-bottom: 0.25rem;">
                                <?= character_limiter($ticket->subject, 50) ?>
                            </div>
                            <div style="font-size: 0.875rem; color: rgba(255, 255, 255, 0.6);">
                                <?= character_limiter($ticket->message, 80) ?>
                            </div>
                        </td>
                        <td>
                            <span class="badge <?= $ticket->priority == 'high' ? 'danger' : ($ticket->priority == 'medium' ? 'warning' : 'info') ?>">
                                <i class="fas fa-<?= $ticket->priority == 'high' ? 'exclamation-triangle' : ($ticket->priority == 'medium' ? 'exclamation-circle' : 'info-circle') ?>"></i>
                                <?= ucfirst($ticket->priority) ?>
                            </span>
                        </td>
                        <td>
                            <span class="badge <?= $ticket->status == 'open' ? 'success' : ($ticket->status == 'answered' ? 'info' : 'secondary') ?>">
                                <i class="fas fa-<?= $ticket->status == 'open' ? 'envelope-open' : ($ticket->status == 'answered' ? 'reply' : 'times') ?>"></i>
                                <?= ucfirst($ticket->status) ?>
                            </span>
                        </td>
                        <td>
                            <div style="color: white; font-weight: 500;"><?= date('d M Y', strtotime($ticket->created_at)) ?></div>
                            <div style="font-size: 0.875rem; color: rgba(255, 255, 255, 0.6);"><?= date('H:i', strtotime($ticket->created_at)) ?></div>
                        </td>
                        <td>
                            <div style="color: rgba(255, 255, 255, 0.8);">
                                <?= date('d M Y H:i', strtotime($ticket->updated_at)) ?>
                            </div>
                        </td>
                        <td>
                            <div style="display: flex; gap: 0.5rem;">
                                <a href="#" class="btn btn-primary btn-sm" title="View Ticket">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <?php if ($ticket->status != 'closed'): ?>
                                <a href="#" class="btn btn-info btn-sm" title="Reply">
                                    <i class="fas fa-reply"></i>
                                </a>
                                <?php endif; ?>
                                <a href="#" class="btn btn-danger btn-sm" title="Close Ticket">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Ticket Statistics -->
<div class="dashboard-section">
    <div class="section-header">
        <h5>Ticket Statistics</h5>
    </div>
    <div style="padding: 2rem;">
        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
            <div style="flex: 1; min-width: 200px;">
                <div style="display: flex; align-items: center; padding: 1rem 1.25rem; background: rgba(34, 197, 94, 0.08); border: 1px solid rgba(34, 197, 94, 0.15); border-radius: 12px; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 15px rgba(34, 197, 94, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                    <div style="color: #22c55e; font-size: 1.5rem; margin-right: 1rem;">
                        <i class="fas fa-envelope-open"></i>
                    </div>
                    <div>
                        <div style="color: white; font-size: 1.1rem; font-weight: 600; margin-bottom: 0.25rem;">
                            <?= count(array_filter($tickets, function($t) { return $t->status == 'open'; })) ?>
                        </div>
                        <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.8rem;">Open Tickets</div>
                    </div>
                </div>
            </div>
            <div style="flex: 1; min-width: 200px;">
                <div style="display: flex; align-items: center; padding: 1rem 1.25rem; background: rgba(14, 165, 233, 0.08); border: 1px solid rgba(14, 165, 233, 0.15); border-radius: 12px; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 15px rgba(14, 165, 233, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                    <div style="color: #0ea5e9; font-size: 1.5rem; margin-right: 1rem;">
                        <i class="fas fa-reply"></i>
                    </div>
                    <div>
                        <div style="color: white; font-size: 1.1rem; font-weight: 600; margin-bottom: 0.25rem;">
                            <?= count(array_filter($tickets, function($t) { return $t->status == 'answered'; })) ?>
                        </div>
                        <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.8rem;">Answered</div>
                    </div>
                </div>
            </div>
            <div style="flex: 1; min-width: 200px;">
                <div style="display: flex; align-items: center; padding: 1rem 1.25rem; background: rgba(107, 114, 128, 0.08); border: 1px solid rgba(107, 114, 128, 0.15); border-radius: 12px; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 15px rgba(107, 114, 128, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                    <div style="color: #6b7280; font-size: 1.5rem; margin-right: 1rem;">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div>
                        <div style="color: white; font-size: 1.1rem; font-weight: 600; margin-bottom: 0.25rem;">
                            <?= count(array_filter($tickets, function($t) { return $t->status == 'closed'; })) ?>
                        </div>
                        <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.8rem;">Closed</div>
                    </div>
                </div>
            </div>
            <div style="flex: 1; min-width: 200px;">
                <div style="display: flex; align-items: center; padding: 1rem 1.25rem; background: rgba(168, 85, 247, 0.08); border: 1px solid rgba(168, 85, 247, 0.15); border-radius: 12px; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 15px rgba(168, 85, 247, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                    <div style="color: #a855f7; font-size: 1.5rem; margin-right: 1rem;">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <div>
                        <div style="color: white; font-size: 1.1rem; font-weight: 600; margin-bottom: 0.25rem;">
                            <?= count($tickets) ?>
                        </div>
                        <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.8rem;">Total Tickets</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Help -->
<div class="dashboard-section">
    <div class="section-header">
        <h5>Quick Help</h5>
    </div>
    <div style="padding: 2rem;">
        <div class="row">
            <div class="col-md-6">
                <div style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 1.5rem; margin-bottom: 1rem;">
                    <h6 style="color: white; margin-bottom: 1rem;">
                        <i class="fas fa-question-circle" style="color: #0ea5e9; margin-right: 0.5rem;"></i>
                        Frequently Asked Questions
                    </h6>
                    <ul style="color: rgba(255, 255, 255, 0.7); font-size: 0.875rem; line-height: 1.6; padding-left: 1rem;">
                        <li>How to order a new server?</li>
                        <li>How to renew my server?</li>
                        <li>How to change server configuration?</li>
                        <li>Payment methods available</li>
                        <li>Server maintenance schedule</li>
                    </ul>
                </div>
            </div>
            <div class="col-md-6">
                <div style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 1.5rem; margin-bottom: 1rem;">
                    <h6 style="color: white; margin-bottom: 1rem;">
                        <i class="fas fa-headset" style="color: #22c55e; margin-right: 0.5rem;"></i>
                        Contact Support
                    </h6>
                    <div style="color: rgba(255, 255, 255, 0.7); font-size: 0.875rem; line-height: 1.6;">
                        <div style="margin-bottom: 0.5rem;">
                            <i class="fas fa-envelope" style="margin-right: 0.5rem;"></i>
                            <EMAIL>
                        </div>
                        <div style="margin-bottom: 0.5rem;">
                            <i class="fab fa-whatsapp" style="margin-right: 0.5rem;"></i>
                            +62 812-3456-7890
                        </div>
                        <div>
                            <i class="fas fa-clock" style="margin-right: 0.5rem;"></i>
                            24/7 Support Available
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 