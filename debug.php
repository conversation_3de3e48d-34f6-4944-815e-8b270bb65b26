<?php
echo "<h1>Debug URL Testing</h1>";

// Check if CodeIgniter is working
if (file_exists('index.php')) {
    echo "<p>✓ index.php found</p>";
} else {
    echo "<p>✗ index.php not found</p>";
}

if (file_exists('application/controllers/Auth.php')) {
    echo "<p>✓ Auth controller found</p>";
} else {
    echo "<p>✗ Auth controller not found</p>";
}

if (file_exists('.htaccess')) {
    echo "<p>✓ .htaccess found</p>";
} else {
    echo "<p>✗ .htaccess not found</p>";
}

echo "<h2>Test Links:</h2>";
echo "<p><a href='index.php/auth/login'>Test dengan index.php/auth/login</a></p>";
echo "<p><a href='auth/login'>Test dengan auth/login</a></p>";
echo "<p><a href='index.php/welcome'>Test dengan index.php/welcome</a></p>";
echo "<p><a href='welcome'>Test dengan welcome</a></p>";

echo "<h2>Server Info:</h2>";
echo "<p>SERVER_NAME: " . $_SERVER['SERVER_NAME'] . "</p>";
echo "<p>REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>SCRIPT_NAME: " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p>DOCUMENT_ROOT: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";

if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "<p style='color: green;'>✓ mod_rewrite AKTIF</p>";
    } else {
        echo "<p style='color: red;'>✗ mod_rewrite TIDAK AKTIF</p>";
    }
}
?> 