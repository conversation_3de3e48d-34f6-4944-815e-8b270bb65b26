<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?><!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Dopminer</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="<?php echo secure_asset('assets/css/style.css'); ?>" rel="stylesheet">
    
    <style>
        /* Admin Dashboard Styles */
        .admin-container {
            display: flex;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            padding-top: 0;
        }
        
        .admin-sidebar {
            width: 260px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 100;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }
        
        .sidebar-header .logo-link {
            display: inline-block;
            margin-bottom: 1rem;
        }
        
        .sidebar-header h3 {
            color: white;
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .nav-item:hover {
            background: rgba(255, 255, 255, 0.05);
            color: white;
        }
        
        .nav-item.active {
            background: rgba(14, 165, 233, 0.1);
            color: #0ea5e9;
            border-left: 3px solid #0ea5e9;
        }
        
        .nav-item i {
            width: 20px;
            text-align: center;
        }
        
        .nav-divider {
            height: 1px;
            background: rgba(255, 255, 255, 0.1);
            margin: 1rem 0;
        }
        
        .admin-main {
            flex: 1;
            margin-left: 260px;
            padding: 2rem;
        }
        
        .admin-content {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        /* Copy dashboard styles from dashboard_header.php */
        .dashboard-welcome {
            margin-bottom: 2rem;
        }
        
        .dashboard-welcome h1 {
            font-size: 2rem;
            font-weight: 700;
            color: white;
            margin: 0 0 0.5rem 0;
            background: linear-gradient(45deg, #0ea5e9, #8b5cf6, #06b6d4);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        .dashboard-welcome p {
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
        }
        
        @keyframes gradientShift {
            0%, 100% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
        }
        
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .stat-card:hover {
            transform: translateY(-4px);
            border-color: #0ea5e9;
            box-shadow: 0 10px 30px rgba(14, 165, 233, 0.3);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        .stat-info {
            flex: 1;
        }
        
        .stat-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }
        
        .stat-value {
            color: white;
            font-size: 1.75rem;
            font-weight: 700;
        }
        
        .dashboard-section {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
            overflow: hidden;
        }
        
        .section-header {
            padding: 1.5rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-header h5 {
            color: white;
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .section-link {
            color: #0ea5e9;
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .section-link:hover {
            color: #38bdf8;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        .dashboard-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .dashboard-table thead th {
            background: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.8);
            padding: 1rem 2rem;
            text-align: left;
            font-weight: 600;
            font-size: 0.875rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .dashboard-table tbody td {
            padding: 1rem 2rem;
            color: rgba(255, 255, 255, 0.9);
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .dashboard-table tbody tr:hover {
            background: rgba(255, 255, 255, 0.02);
        }
        
        .badge {
            padding: 0.25rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .badge.success { background: rgba(34, 197, 94, 0.2); color: #22c55e; }
        .badge.warning { background: rgba(245, 158, 11, 0.2); color: #f59e0b; }
        .badge.danger { background: rgba(239, 68, 68, 0.2); color: #ef4444; }
        .badge.info { background: rgba(168, 85, 247, 0.2); color: #a855f7; }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            border: none;
            cursor: pointer;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem 2rem;
            color: rgba(255, 255, 255, 0.6);
        }
        
        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: rgba(255, 255, 255, 0.3);
        }
        
        .quick-action-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
            text-align: center;
            height: 120px;
        }
        
        .quick-action-card:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .quick-action-card i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #0ea5e9;
        }
        
        .quick-action-card span {
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: -0.5rem;
        }
        
        .col-md-3 {
            flex: 0 0 25%;
            max-width: 25%;
            padding: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .admin-sidebar {
                width: 200px;
            }
            
            .admin-main {
                margin-left: 200px;
            }
            
            .col-md-3 {
                flex: 0 0 50%;
                max-width: 50%;
            }
        }
        
        @media (max-width: 576px) {
            .admin-sidebar {
                display: none;
            }
            
            .admin-main {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container" style="padding-top: 0;">
        <div class="admin-sidebar" style="height: 100vh;">
            <div class="sidebar-header">
                <a href="<?= base_url() ?>" class="logo-link">
                    <img src="<?= base_url('assets/images/logo.png') ?>" alt="Dopminer" style="height: 40px;">
                </a>
                <h3><i class="fas fa-user-shield"></i> Admin Panel</h3>
            </div>
            <nav class="sidebar-nav">
                <a href="<?= base_url('admin') ?>" class="nav-item active">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="<?= base_url('admin/users') ?>" class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>Manage Users</span>
                </a>
                <a href="<?= base_url('admin/server_plans') ?>" class="nav-item">
                    <i class="fas fa-server"></i>
                    <span>Server Plans</span>
                </a>
                <a href="<?= base_url('admin/orders') ?>" class="nav-item">
                    <i class="fas fa-shopping-cart"></i>
                    <span>Orders</span>
                </a>
                <a href="<?= base_url('admin/tickets') ?>" class="nav-item">
                    <i class="fas fa-ticket-alt"></i>
                    <span>Support Tickets</span>
                </a>
                <a href="<?= base_url('admin/transactions') ?>" class="nav-item">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>Transactions</span>
                </a>
                <a href="<?= base_url('admin/settings') ?>" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
                <div class="nav-divider"></div>
                <a href="<?= base_url('dashboard') ?>" class="nav-item">
                    <i class="fas fa-arrow-left"></i>
                    <span>Back to User Dashboard</span>
                </a>
            </nav>
        </div>
        
        <div class="admin-main">
            <div class="admin-content">
                <div class="dashboard-welcome">
                    <h1 id="welcomeText">Welcome to Admin Panel, <?= $user->username ?>!</h1>
                    <p>Manage your hosting platform from here</p>
                </div>

                <!-- Admin Statistics -->
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, #0ea5e9, #0284c7);">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-label">Total Users</div>
                            <div class="stat-value counter" data-target="<?= isset($total_users) ? $total_users : 0 ?>">0</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, #22c55e, #16a34a);">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-label">Active Servers</div>
                            <div class="stat-value counter" data-target="<?= isset($total_servers) ? $total_servers : 0 ?>">0</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-label">Total Orders</div>
                            <div class="stat-value counter" data-target="<?= isset($total_orders) ? $total_orders : 0 ?>">0</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-label">Open Tickets</div>
                            <div class="stat-value counter" data-target="<?= isset($total_tickets) ? $total_tickets : 0 ?>">0</div>
                        </div>
                    </div>
                </div>

                <!-- Recent Orders -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <h5><i class="fas fa-clock"></i> Recent Orders</h5>
                        <a href="<?= base_url('admin/orders') ?>" class="section-link">View All</a>
                    </div>
                    <div class="table-responsive">
                        <table class="dashboard-table">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>User</th>
                                    <th>Plan</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (isset($recent_orders) && !empty($recent_orders)): ?>
                                    <?php foreach ($recent_orders as $order): ?>
                                    <tr>
                                        <td>#<?= str_pad($order->id, 5, '0', STR_PAD_LEFT) ?></td>
                                        <td><?= isset($order->username) ? $order->username : 'N/A' ?></td>
                                        <td><?= isset($order->plan_name) ? $order->plan_name : 'N/A' ?></td>
                                        <td>Rp <?= isset($order->amount) ? number_format($order->amount, 0, ',', '.') : 0 ?></td>
                                        <td>
                                            <span class="badge <?= isset($order->status) && $order->status == 'active' ? 'success' : 'warning' ?>">
                                                <?= isset($order->status) ? ucfirst($order->status) : 'Pending' ?>
                                            </span>
                                        </td>
                                        <td><?= isset($order->created_at) ? date('d M Y', strtotime($order->created_at)) : 'N/A' ?></td>
                                        <td>
                                            <a href="<?= base_url('admin/order_details/' . $order->id) ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7">
                                            <div class="empty-state">
                                                <i class="fas fa-shopping-cart"></i>
                                                <div>No recent orders found</div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Unassigned Tickets -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <h5><i class="fas fa-ticket-alt"></i> Unassigned Tickets</h5>
                        <a href="<?= base_url('admin/tickets') ?>" class="section-link">View All</a>
                    </div>
                    <div class="table-responsive">
                        <table class="dashboard-table">
                            <thead>
                                <tr>
                                    <th>Ticket #</th>
                                    <th>User</th>
                                    <th>Subject</th>
                                    <th>Priority</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (isset($unassigned_tickets) && !empty($unassigned_tickets)): ?>
                                    <?php foreach ($unassigned_tickets as $ticket): ?>
                                    <tr>
                                        <td>#<?= $ticket->ticket_number ?></td>
                                        <td><?= isset($ticket->username) ? $ticket->username : 'N/A' ?></td>
                                        <td><?= $ticket->subject ?></td>
                                        <td>
                                            <span class="badge <?= $ticket->priority == 'high' ? 'danger' : ($ticket->priority == 'medium' ? 'warning' : 'info') ?>">
                                                <?= ucfirst($ticket->priority) ?>
                                            </span>
                                        </td>
                                        <td><?= date('d M Y H:i', strtotime($ticket->created_at)) ?></td>
                                        <td>
                                            <a href="<?= base_url('admin/ticket_details/' . $ticket->id) ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="6">
                                            <div class="empty-state">
                                                <i class="fas fa-ticket-alt"></i>
                                                <div>No unassigned tickets</div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                    </div>
                    <div style="padding: 2rem;">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="<?= base_url('admin/add_plan') ?>" class="quick-action-card">
                                    <i class="fas fa-plus-circle"></i>
                                    <span>Add New Plan</span>
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="<?= base_url('admin/users/add') ?>" class="quick-action-card">
                                    <i class="fas fa-user-plus"></i>
                                    <span>Add New User</span>
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="<?= base_url('admin/announcements') ?>" class="quick-action-card">
                                    <i class="fas fa-bullhorn"></i>
                                    <span>Announcements</span>
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="<?= base_url('admin/settings') ?>" class="quick-action-card">
                                    <i class="fas fa-cog"></i>
                                    <span>Settings</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="<?= secure_asset('assets/js/main.js') ?>"></script>
    <script>
        // Admin specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Animate counter numbers
            const counters = document.querySelectorAll('.counter');
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                let current = 0;
                const increment = target / 50;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        counter.textContent = target;
                        clearInterval(timer);
                    } else {
                        counter.textContent = Math.floor(current);
                    }
                }, 20);
            });
            
            // Add active class to current page in sidebar
            const currentPath = window.location.pathname;
            document.querySelectorAll('.nav-item').forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    item.classList.add('active');
                }
            });
        });
    </script>
</body>
</html> 