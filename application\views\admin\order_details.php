<?php
defined('BASEPATH') OR exit('No direct script access allowed');
$data['title'] = $title ?? 'Order Details';

$content = '<div class="page-header">
    <h1>Order Details</h1>
    <p>View and manage customer order</p>
</div>';

if (isset($order) && $order) {
    $payment_status_class = '';
    switch($order->payment_status) {
        case 'paid': $payment_status_class = 'success'; break;
        case 'pending': $payment_status_class = 'warning'; break;
        case 'failed': $payment_status_class = 'danger'; break;
        case 'cancelled': $payment_status_class = 'secondary'; break;
        default: $payment_status_class = 'info';
    }
    
    $server_status_class = '';
    switch($order->server_status) {
        case 'active': $server_status_class = 'success'; break;
        case 'pending': $server_status_class = 'warning'; break;
        case 'suspended': $server_status_class = 'danger'; break;
        case 'terminated': $server_status_class = 'secondary'; break;
        default: $server_status_class = 'info';
    }

    $content .= '<div class="admin-card">
        <div class="card-header">
            <h2><i class="fas fa-shopping-cart"></i> Order #' . $order->order_number . '</h2>
            <div class="order-badges">
                <span class="badge ' . $payment_status_class . '">' . ucfirst($order->payment_status) . '</span>
                <span class="badge ' . $server_status_class . '">' . ucfirst($order->server_status) . '</span>
            </div>
        </div>
        <div style="padding: 2rem;">
            <div class="order-info">
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Customer:</span>
                        <span class="info-value">' . htmlspecialchars($order->username ?? 'Unknown') . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Email:</span>
                        <span class="info-value">' . htmlspecialchars($order->email ?? 'N/A') . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Full Name:</span>
                        <span class="info-value">' . htmlspecialchars($order->full_name ?? 'N/A') . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Plan:</span>
                        <span class="info-value">' . htmlspecialchars($order->plan_name ?? 'Unknown Plan') . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Billing Cycle:</span>
                        <span class="info-value">' . ucfirst($order->billing_cycle) . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Total Amount:</span>
                        <span class="info-value">Rp ' . number_format($order->total_amount, 0, ',', '.') . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Order Date:</span>
                        <span class="info-value">' . date('M j, Y H:i', strtotime($order->created_at)) . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Expires At:</span>
                        <span class="info-value">' . ($order->expires_at ? date('M j, Y H:i', strtotime($order->expires_at)) : 'N/A') . '</span>
                    </div>
                </div>
            </div>
        </div>
    </div>';

    // Server Configuration
    if (isset($order->specifications) && $order->specifications) {
        $specs = json_decode($order->specifications, true);
        if ($specs) {
            $content .= '<div class="admin-card">
                <div class="card-header">
                    <h2><i class="fas fa-server"></i> Server Specifications</h2>
                </div>
                <div style="padding: 2rem;">
                    <div class="specs-grid">
                        <div class="spec-item">
                            <span class="spec-label">CPU:</span>
                            <span class="spec-value">' . ($specs['cpu'] ?? 'N/A') . '</span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label">RAM:</span>
                            <span class="spec-value">' . ($specs['ram'] ?? 'N/A') . '</span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label">Storage:</span>
                            <span class="spec-value">' . ($specs['storage'] ?? 'N/A') . '</span>
                        </div>
                        <div class="spec-item">
                            <span class="spec-label">Bandwidth:</span>
                            <span class="spec-value">' . ($specs['bandwidth'] ?? 'N/A') . '</span>
                        </div>
                    </div>
                </div>
            </div>';
        }
    }

    // Update Status Form
    $content .= '<div class="admin-card">
        <div class="card-header">
            <h2><i class="fas fa-edit"></i> Update Order Status</h2>
        </div>
        <div style="padding: 2rem;">
            <form method="post" action="' . base_url('admin/order_details/' . $order->id) . '">
                <div class="form-row">
                    <div class="form-group">
                        <label for="payment_status">Payment Status</label>
                        <select name="payment_status" id="payment_status" class="form-control">
                            <option value="pending" ' . ($order->payment_status == 'pending' ? 'selected' : '') . '>Pending</option>
                            <option value="paid" ' . ($order->payment_status == 'paid' ? 'selected' : '') . '>Paid</option>
                            <option value="failed" ' . ($order->payment_status == 'failed' ? 'selected' : '') . '>Failed</option>
                            <option value="cancelled" ' . ($order->payment_status == 'cancelled' ? 'selected' : '') . '>Cancelled</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="server_status">Server Status</label>
                        <select name="server_status" id="server_status" class="form-control">
                            <option value="pending" ' . ($order->server_status == 'pending' ? 'selected' : '') . '>Pending</option>
                            <option value="active" ' . ($order->server_status == 'active' ? 'selected' : '') . '>Active</option>
                            <option value="suspended" ' . ($order->server_status == 'suspended' ? 'selected' : '') . '>Suspended</option>
                            <option value="terminated" ' . ($order->server_status == 'terminated' ? 'selected' : '') . '>Terminated</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="admin_notes">Admin Notes</label>
                    <textarea name="admin_notes" id="admin_notes" class="form-control" rows="3" 
                              placeholder="Add internal notes about this order...">' . ($order->admin_notes ?? '') . '</textarea>
                </div>
                
                <div class="form-actions">
                    <a href="' . base_url('admin/orders') . '" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Orders
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Order
                    </button>
                </div>
            </form>
        </div>
    </div>';

    // Order History/Timeline
    $content .= '<div class="admin-card">
        <div class="card-header">
            <h2><i class="fas fa-history"></i> Order Timeline</h2>
        </div>
        <div style="padding: 2rem;">
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-marker success"></div>
                    <div class="timeline-content">
                        <h4>Order Created</h4>
                        <p>Order was placed by customer</p>
                        <small>' . date('M j, Y H:i', strtotime($order->created_at)) . '</small>
                    </div>
                </div>';

    if ($order->payment_status == 'paid') {
        $content .= '<div class="timeline-item">
                        <div class="timeline-marker success"></div>
                        <div class="timeline-content">
                            <h4>Payment Confirmed</h4>
                            <p>Payment has been received and confirmed</p>
                            <small>' . ($order->paid_at ? date('M j, Y H:i', strtotime($order->paid_at)) : 'N/A') . '</small>
                        </div>
                    </div>';
    }

    if ($order->server_status == 'active') {
        $content .= '<div class="timeline-item">
                        <div class="timeline-marker success"></div>
                        <div class="timeline-content">
                            <h4>Server Activated</h4>
                            <p>Server has been set up and activated</p>
                            <small>' . ($order->activated_at ? date('M j, Y H:i', strtotime($order->activated_at)) : 'N/A') . '</small>
                        </div>
                    </div>';
    }

    $content .= '</div>
        </div>
    </div>';

} else {
    $content .= '<div class="alert alert-danger">Order not found.</div>';
}

$content .= '<style>
.order-badges {
    display: flex;
    gap: 0.5rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.info-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
    font-weight: 500;
}

.info-value {
    color: white;
    font-weight: 600;
}

.specs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.spec-label {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.spec-value {
    color: white;
    font-weight: 600;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    color: white;
    font-weight: 500;
    margin-bottom: 0.5rem;
}



.form-actions {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
}

.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: "";
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: rgba(255, 255, 255, 0.1);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    top: 0.25rem;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.1);
}

.timeline-marker.success {
    background: #22c55e;
    border-color: #22c55e;
}

.timeline-content h4 {
    color: white;
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
}

.timeline-content p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0 0 0.5rem 0;
}

.timeline-content small {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.875rem;
}

.badge.secondary {
    background: rgba(156, 163, 175, 0.2);
    color: #9ca3af;
}
</style>';

$data['content'] = $content;
$this->load->view('admin/admin_template', $data);
?> 