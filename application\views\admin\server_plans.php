<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?><!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?> - Admin Panel - Dopminer</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="<?php echo secure_asset('assets/css/style.css'); ?>" rel="stylesheet">
    
    <style>
        /* Admin Dashboard Styles */
        .admin-container {
            display: flex;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            padding-top: 0;
        }
        
        .admin-sidebar {
            width: 260px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 100;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }
        
        .sidebar-header .logo-link {
            display: inline-block;
            margin-bottom: 1rem;
        }
        
        .sidebar-header h3 {
            color: white;
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .nav-item:hover {
            background: rgba(255, 255, 255, 0.05);
            color: white;
        }
        
        .nav-item.active {
            background: rgba(14, 165, 233, 0.1);
            color: #0ea5e9;
            border-left: 3px solid #0ea5e9;
        }
        
        .nav-item i {
            width: 20px;
            text-align: center;
        }
        
        .nav-divider {
            height: 1px;
            background: rgba(255, 255, 255, 0.1);
            margin: 1rem 0;
        }
        
        .admin-main {
            flex: 1;
            margin-left: 260px;
            padding: 2rem;
        }
        
        .admin-content {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            margin-bottom: 2rem;
        }
        
        .page-header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: white;
            margin: 0 0 0.5rem 0;
        }
        
        .page-header p {
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
        }
        
        .admin-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .card-header {
            padding: 1.5rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-header h2 {
            color: white;
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        .admin-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .admin-table thead th {
            background: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.8);
            padding: 1rem 2rem;
            text-align: left;
            font-weight: 600;
            font-size: 0.875rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .admin-table tbody td {
            padding: 1rem 2rem;
            color: rgba(255, 255, 255, 0.9);
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .admin-table tbody tr:hover {
            background: rgba(255, 255, 255, 0.02);
        }
        
        .badge {
            padding: 0.25rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .badge.success { background: rgba(34, 197, 94, 0.2); color: #22c55e; }
        .badge.warning { background: rgba(245, 158, 11, 0.2); color: #f59e0b; }
        .badge.danger { background: rgba(239, 68, 68, 0.2); color: #ef4444; }
        .badge.info { background: rgba(168, 85, 247, 0.2); color: #a855f7; }
        
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            border: none;
            cursor: pointer;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .alert {
            padding: 1rem 1.5rem;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            border: 1px solid;
        }
        
        .alert-success {
            background: rgba(34, 197, 94, 0.1);
            border-color: rgba(34, 197, 94, 0.3);
            color: #22c55e;
        }
        
        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-sidebar">
            <div class="sidebar-header">
                <a href="<?= base_url() ?>" class="logo-link">
                    <img src="<?= base_url('assets/images/logo.png') ?>" alt="Dopminer" style="height: 40px;">
                </a>
                <h3><i class="fas fa-user-shield"></i> Admin Panel</h3>
            </div>
            <nav class="sidebar-nav">
                <a href="<?= base_url('admin') ?>" class="nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="<?= base_url('admin/users') ?>" class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>Manage Users</span>
                </a>
                <a href="<?= base_url('admin/server_plans') ?>" class="nav-item active">
                    <i class="fas fa-server"></i>
                    <span>Server Plans</span>
                </a>
                <a href="<?= base_url('admin/orders') ?>" class="nav-item">
                    <i class="fas fa-shopping-cart"></i>
                    <span>Orders</span>
                </a>
                <a href="<?= base_url('admin/tickets') ?>" class="nav-item">
                    <i class="fas fa-ticket-alt"></i>
                    <span>Support Tickets</span>
                </a>
                <a href="<?= base_url('admin/transactions') ?>" class="nav-item">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>Transactions</span>
                </a>
                <a href="<?= base_url('admin/settings') ?>" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
                <div class="nav-divider"></div>
                <a href="<?= base_url('dashboard') ?>" class="nav-item">
                    <i class="fas fa-arrow-left"></i>
                    <span>Back to User Dashboard</span>
                </a>
            </nav>
        </div>
        
        <div class="admin-main">
            <div class="admin-content">
                <div class="page-header">
                    <h1>Server Plans</h1>
                    <p>Manage all server hosting plans</p>
                </div>
                
                <?php if ($this->session->flashdata('success')): ?>
                    <div class="alert alert-success">
                        <?= $this->session->flashdata('success') ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($this->session->flashdata('error')): ?>
                    <div class="alert alert-danger">
                        <?= $this->session->flashdata('error') ?>
                    </div>
                <?php endif; ?>
                
                <div class="admin-card">
                    <div class="card-header">
                        <h2><i class="fas fa-server"></i> All Server Plans</h2>
                        <a href="<?= base_url('admin/add_plan') ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add New Plan
                        </a>
                    </div>
                    <div class="table-responsive">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Category</th>
                                    <th>Plan Name</th>
                                    <th>Specifications</th>
                                    <th>Monthly Price</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (isset($plans) && !empty($plans)): ?>
                                    <?php foreach ($plans as $plan): ?>
                                    <?php 
                                        // Safe JSON decode with error handling
                                        $specs = [];
                                        if (!empty($plan->specifications)) {
                                            $decoded = json_decode($plan->specifications, true);
                                            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                                                $specs = $decoded;
                                            }
                                        }
                                    ?>
                                    <tr>
                                        <td>#<?= $plan->id ?></td>
                                        <td><?= $plan->category_name ?? 'N/A' ?></td>
                                        <td><?= $plan->name ?></td>
                                        <td>
                                            <small>
                                                CPU: <?= $specs['cpu'] ?? 'N/A' ?><br>
                                                RAM: <?= $specs['ram'] ?? 'N/A' ?><br>
                                                Storage: <?= $specs['storage'] ?? 'N/A' ?>
                                            </small>
                                        </td>
                                        <td>Rp <?= number_format($plan->price_monthly, 0, ',', '.') ?></td>
                                        <td>
                                            <span class="badge <?= $plan->status == 'active' ? 'success' : 'warning' ?>">
                                                <?= ucfirst($plan->status) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="<?= base_url('admin/edit_plan/' . $plan->id) ?>" class="btn btn-sm btn-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="<?= base_url('admin/delete_plan/' . $plan->id) ?>" 
                                                   class="btn btn-sm btn-danger"
                                                   onclick="return confirm('Are you sure you want to delete this plan?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7" style="text-align: center; color: rgba(255, 255, 255, 0.5);">
                                            No server plans found
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="<?= secure_asset('assets/js/main.js') ?>"></script>
</body>
</html> 