<?php
defined('BASEPATH') OR exit('No direct script access allowed');
// Use admin template with transactions content
$data['title'] = $title ?? 'Transactions';
$data['content'] = '
<div class="page-header">
    <h1>Transaction Management</h1>
    <p>View all payment transactions</p>
</div>

<div class="dashboard-stats" style="margin-bottom: 2rem;">
    <div class="stat-card">
        <div class="stat-icon" style="background: linear-gradient(135deg, #22c55e, #16a34a);">
            <i class="fas fa-money-bill-wave"></i>
        </div>
        <div class="stat-info">
            <div class="stat-label">Total Revenue</div>
            <div class="stat-value">Rp ' . number_format($total_revenue ?? 0, 0, ',', '.') . '</div>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
            <i class="fas fa-clock"></i>
        </div>
        <div class="stat-info">
            <div class="stat-label">Pending Revenue</div>
            <div class="stat-value">Rp ' . number_format($pending_revenue ?? 0, 0, ',', '.') . '</div>
        </div>
    </div>
</div>

<div class="admin-card">
    <div class="card-header">
        <h2><i class="fas fa-money-bill-wave"></i> All Transactions</h2>
    </div>
    <div class="table-responsive">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>Transaction ID</th>
                    <th>User</th>
                    <th>Type</th>
                    <th>Description</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th>Payment Method</th>
                    <th>Date</th>
                </tr>
            </thead>
            <tbody>';

if (isset($transactions) && !empty($transactions)):
    foreach ($transactions as $transaction):
        $data['content'] .= '
                <tr>
                    <td>#TRX' . str_pad($transaction->id, 8, '0', STR_PAD_LEFT) . '</td>
                    <td>' . ($transaction->username ?? 'N/A') . '<br><small>' . ($transaction->email ?? '') . '</small></td>
                    <td>
                        <span class="badge ' . ($transaction->type == 'credit' ? 'success' : 'danger') . '">
                            ' . ucfirst($transaction->type ?? 'credit') . '
                        </span>
                    </td>
                    <td>' . ($transaction->description ?? 'Payment') . '</td>
                    <td>Rp ' . number_format($transaction->amount ?? 0, 0, ',', '.') . '</td>
                    <td>
                        <span class="badge ' . ($transaction->status == 'completed' ? 'success' : ($transaction->status == 'failed' ? 'danger' : 'warning')) . '">
                            ' . ucfirst($transaction->status ?? 'pending') . '
                        </span>
                    </td>
                    <td>' . ucfirst($transaction->payment_method ?? 'manual') . '</td>
                    <td>' . date('d M Y H:i', strtotime($transaction->created_at)) . '</td>
                </tr>';
    endforeach;
else:
    $data['content'] .= '
                <tr>
                    <td colspan="8" style="text-align: center; color: rgba(255, 255, 255, 0.5);">
                        No transactions found
                    </td>
                </tr>';
endif;

$data['content'] .= '
            </tbody>
        </table>
    </div>
</div>

<style>
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}
.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}
.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}
.stat-info {
    flex: 1;
}
.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}
.stat-value {
    color: white;
    font-size: 1.75rem;
    font-weight: 700;
}
</style>';

// Load admin template with transactions content
$this->load->view('admin/admin_template', $data);
?> 