# Simple Anti-Hotlinking Protection for CSS and JS
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Block direct access (no referer) to CSS and JS files
    RewriteCond %{REQUEST_FILENAME} \.(css|js)$ [NC]
    RewriteCond %{HTTP_REFERER} ^$
    RewriteRule \.(css|js)$ /dopminer/assets/blocked.html [R=403,L]
    
    # Block access from external domains (anti-hotlinking)
    RewriteCond %{REQUEST_FILENAME} \.(css|js)$ [NC]
    RewriteCond %{HTTP_REFERER} !^http://localhost [NC]
    RewriteCond %{HTTP_REFERER} !^https://localhost [NC]
    RewriteCond %{HTTP_REFERER} !^http://127\.0\.0\.1 [NC]
    RewriteCond %{HTTP_REFERER} !^https://127\.0\.0\.1 [NC]
    RewriteRule \.(css|js)$ /dopminer/assets/blocked.html [R=403,L]
</IfModule>

# Disable directory browsing
Options -Indexes

# Prevent access to sensitive files
<Files ~ "^.*\.([Hh][Tt][Aa])">
    Order allow,deny
    Deny from all
    Satisfy all
</Files>

# Add security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Cache control for better performance
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule> 