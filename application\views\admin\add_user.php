<?php
defined('BASEPATH') OR exit('No direct script access allowed');
$data['title'] = $title ?? 'Add User';

$content = '<div class="page-header">
    <h1>Add New User</h1>
    <p>Create a new user account</p>
</div>';

// Show validation errors
if (validation_errors()) {
    $content .= '<div class="alert alert-danger">' . validation_errors() . '</div>';
}

$content .= '<form method="post" action="' . base_url('admin/add_user') . '">
    <div class="admin-card">
        <div class="card-header">
            <h2><i class="fas fa-user-plus"></i> User Information</h2>
        </div>
        <div style="padding: 2rem;">
            <div class="form-grid">
                <div class="form-group">
                    <label for="username">Username *</label>
                    <input type="text" name="username" id="username" class="form-control" 
                           value="' . set_value('username') . '" required>
                </div>
                
                <div class="form-group">
                    <label for="email">Email *</label>
                    <input type="email" name="email" id="email" class="form-control" 
                           value="' . set_value('email') . '" required>
                </div>
                
                <div class="form-group">
                    <label for="full_name">Full Name</label>
                    <input type="text" name="full_name" id="full_name" class="form-control" 
                           value="' . set_value('full_name') . '">
                </div>
                
                <div class="form-group">
                    <label for="password">Password *</label>
                    <input type="password" name="password" id="password" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="role">Role *</label>
                    <select name="role" id="role" class="form-control" required>
                        <option value="user" ' . set_select('role', 'user', true) . '>User</option>
                        <option value="admin" ' . set_select('role', 'admin') . '>Admin</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="status">Status *</label>
                    <select name="status" id="status" class="form-control" required>
                        <option value="active" ' . set_select('status', 'active', true) . '>Active</option>
                        <option value="inactive" ' . set_select('status', 'inactive') . '>Inactive</option>
                        <option value="suspended" ' . set_select('status', 'suspended') . '>Suspended</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="balance">Initial Balance</label>
                    <input type="number" name="balance" id="balance" class="form-control" 
                           value="' . set_value('balance', '0') . '" min="0" step="0.01">
                </div>
                
                <div class="form-group">
                    <label for="phone">Phone Number</label>
                    <input type="text" name="phone" id="phone" class="form-control" 
                           value="' . set_value('phone') . '">
                </div>
            </div>
        </div>
    </div>
    
    <div class="form-actions">
        <a href="' . base_url('admin/users') . '" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Users
        </a>
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-save"></i> Create User
        </button>
    </div>
</form>';

$content .= '<style>
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}
.form-actions {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}
.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
}
.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
}
</style>';

$data['content'] = $content;
$this->load->view('admin/admin_template', $data);
?> 