<?php if ($this->session->flashdata('success')): ?>
    <div class="alert alert-success">
        <?= $this->session->flashdata('success') ?>
    </div>
<?php endif; ?>

<?php if ($this->session->flashdata('error')): ?>
    <div class="alert alert-danger">
        <?= $this->session->flashdata('error') ?>
    </div>
<?php endif; ?>

<?php if (validation_errors()): ?>
    <div class="alert alert-danger">
        <?= validation_errors() ?>
    </div>
<?php endif; ?>

<!-- Change Password Section -->
<div class="dashboard-welcome">
    <h1>Change Password</h1>
    <a href="<?= base_url('dashboard/profile') ?>" class="btn" style="background: rgba(255, 255, 255, 0.1); color: white;">
        <i class="fas fa-arrow-left"></i> Back to Profile
    </a>
</div>

<!-- Change Password Form -->
<div class="dashboard-section">
    <div class="section-header">
        <h5>Security Settings</h5>
    </div>
    <div style="display: flex; justify-content: center; padding: 1.5rem;">
        <div style="width: 100%; max-width: 450px;">
            <form method="POST" action="<?= base_url('dashboard/change_password') ?>">
                <div style="margin-bottom: 1.25rem;">
                    <label for="current_password" style="color: rgba(255, 255, 255, 0.8); margin-bottom: 0.5rem; display: block; font-weight: 500; font-size: 0.9rem;">Current Password <span style="color: #ef4444;">*</span></label>
                    <input type="password" id="current_password" name="current_password" class="form-control" required style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.15); color: white; padding: 0.625rem 0.875rem; border-radius: 6px; font-size: 0.875rem; transition: all 0.3s ease; width: 100%;" onfocus="this.style.borderColor='#0ea5e9'; this.style.boxShadow='0 0 0 2px rgba(14, 165, 233, 0.1)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.15)'; this.style.boxShadow='none'">
                </div>
                
                <div style="margin-bottom: 1.25rem;">
                    <label for="new_password" style="color: rgba(255, 255, 255, 0.8); margin-bottom: 0.5rem; display: block; font-weight: 500; font-size: 0.9rem;">New Password <span style="color: #ef4444;">*</span></label>
                    <input type="password" id="new_password" name="new_password" class="form-control" required style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.15); color: white; padding: 0.625rem 0.875rem; border-radius: 6px; font-size: 0.875rem; transition: all 0.3s ease; width: 100%;" onfocus="this.style.borderColor='#0ea5e9'; this.style.boxShadow='0 0 0 2px rgba(14, 165, 233, 0.1)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.15)'; this.style.boxShadow='none'">
                    <small style="color: rgba(255, 255, 255, 0.6); font-size: 0.75rem; margin-top: 0.375rem; display: block;">
                        <i class="fas fa-info-circle" style="margin-right: 0.25rem;"></i>
                        Minimum 6 characters required
                    </small>
                </div>
                
                <div style="margin-bottom: 1.5rem;">
                    <label for="confirm_password" style="color: rgba(255, 255, 255, 0.8); margin-bottom: 0.5rem; display: block; font-weight: 500; font-size: 0.9rem;">Confirm New Password <span style="color: #ef4444;">*</span></label>
                    <input type="password" id="confirm_password" name="confirm_password" class="form-control" required style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.15); color: white; padding: 0.625rem 0.875rem; border-radius: 6px; font-size: 0.875rem; transition: all 0.3s ease; width: 100%;" onfocus="this.style.borderColor='#0ea5e9'; this.style.boxShadow='0 0 0 2px rgba(14, 165, 233, 0.1)'" onblur="this.style.borderColor='rgba(255, 255, 255, 0.15)'; this.style.boxShadow='none'">
                </div>
                
                <!-- Security Tips -->
                <div style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 6px; padding: 1rem; margin-bottom: 1.5rem;">
                    <h6 style="color: white; margin-bottom: 0.75rem; font-size: 0.85rem;">
                        <i class="fas fa-shield-alt" style="color: #0ea5e9; margin-right: 0.5rem;"></i>
                        Password Security Tips
                    </h6>
                    <ul style="color: rgba(255, 255, 255, 0.7); font-size: 0.75rem; line-height: 1.4; padding-left: 1rem; margin: 0;">
                        <li>Use at least 6 characters</li>
                        <li>Include uppercase and lowercase letters</li>
                        <li>Add numbers and special characters</li>
                        <li>Don't use personal information</li>
                    </ul>
                </div>
                
                <div style="padding-top: 1.25rem; border-top: 1px solid rgba(255, 255, 255, 0.1); text-align: center;">
                    <button type="submit" class="btn btn-primary" style="padding: 0.625rem 1.25rem; font-weight: 600; border-radius: 6px; font-size: 0.875rem;">
                        <i class="fas fa-key" style="margin-right: 0.5rem;"></i> Change Password
                    </button>
                    <a href="<?= base_url('dashboard/profile') ?>" class="btn" style="background: rgba(255, 255, 255, 0.08); color: rgba(255, 255, 255, 0.8); margin-left: 0.75rem; padding: 0.625rem 1.25rem; border-radius: 6px; text-decoration: none; transition: all 0.3s ease; font-size: 0.875rem;" onmouseover="this.style.background='rgba(255, 255, 255, 0.12)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.08)'">
                        <i class="fas fa-times" style="margin-right: 0.5rem;"></i> Cancel
                    </a>
                </div><br><br><br>
            </form>
        </div>
    </div>
</div> 