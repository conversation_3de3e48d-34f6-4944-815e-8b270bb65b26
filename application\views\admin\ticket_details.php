<?php
defined('BASEPATH') OR exit('No direct script access allowed');
$data['title'] = $title ?? 'Ticket Details';

$content = '<div class="page-header">
    <h1>Ticket Details</h1>
    <p>View and manage support ticket</p>
</div>';

if (isset($ticket) && $ticket) {
    $status_class = '';
    switch($ticket->status) {
        case 'open': $status_class = 'info'; break;
        case 'in_progress': $status_class = 'warning'; break;
        case 'resolved': $status_class = 'success'; break;
        case 'closed': $status_class = 'secondary'; break;
        default: $status_class = 'info';
    }
    
    $priority_class = '';
    switch($ticket->priority) {
        case 'low': $priority_class = 'info'; break;
        case 'medium': $priority_class = 'warning'; break;
        case 'high': $priority_class = 'danger'; break;
        case 'urgent': $priority_class = 'danger'; break;
        default: $priority_class = 'info';
    }

    $content .= '<div class="admin-card">
        <div class="card-header">
            <h2><i class="fas fa-ticket-alt"></i> Ticket #' . $ticket->ticket_number . '</h2>
            <div class="ticket-badges">
                <span class="badge ' . $status_class . '">' . ucfirst($ticket->status) . '</span>
                <span class="badge ' . $priority_class . '">' . ucfirst($ticket->priority) . '</span>
            </div>
        </div>
        <div style="padding: 2rem;">
            <div class="ticket-info">
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Subject:</span>
                        <span class="info-value">' . htmlspecialchars($ticket->subject) . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Customer:</span>
                        <span class="info-value">' . htmlspecialchars($ticket->username ?? 'Unknown') . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Email:</span>
                        <span class="info-value">' . htmlspecialchars($ticket->email ?? 'N/A') . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Category:</span>
                        <span class="info-value">' . htmlspecialchars($ticket->category ?? 'General') . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Created:</span>
                        <span class="info-value">' . date('M j, Y H:i', strtotime($ticket->created_at)) . '</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Last Updated:</span>
                        <span class="info-value">' . date('M j, Y H:i', strtotime($ticket->updated_at)) . '</span>
                    </div>
                </div>
            </div>
        </div>
    </div>';

    // Messages/Conversation
    $content .= '<div class="admin-card">
        <div class="card-header">
            <h2><i class="fas fa-comments"></i> Conversation</h2>
        </div>
        <div class="ticket-messages">';

    if (isset($messages) && !empty($messages)) {
        foreach ($messages as $message) {
            $is_admin = ($message->role == 'admin');
            $message_class = $is_admin ? 'admin-message' : 'user-message';
            
            $content .= '<div class="message ' . $message_class . '">
                <div class="message-header">
                    <div class="message-author">
                        <i class="fas fa-' . ($is_admin ? 'user-shield' : 'user') . '"></i>
                        ' . htmlspecialchars($message->username ?? 'Unknown') . '
                        ' . ($is_admin ? '<span class="admin-badge">Admin</span>' : '') . '
                    </div>
                    <div class="message-time">' . date('M j, Y H:i', strtotime($message->created_at)) . '</div>
                </div>
                <div class="message-content">' . nl2br(htmlspecialchars($message->message)) . '</div>';
            
            if ($message->is_internal) {
                $content .= '<div class="internal-note">
                    <i class="fas fa-eye-slash"></i> Internal Note (Not visible to customer)
                </div>';
            }
            
            $content .= '</div>';
        }
    } else {
        $content .= '<div class="empty-messages">
            <i class="fas fa-comments"></i>
            <p>No messages yet</p>
        </div>';
    }

    $content .= '</div>
    </div>';

    // Reply Form
    $content .= '<div class="admin-card">
        <div class="card-header">
            <h2><i class="fas fa-reply"></i> Reply to Ticket</h2>
        </div>
        <div style="padding: 2rem;">
            <form method="post" action="' . base_url('admin/ticket_details/' . $ticket->id) . '">
                <div class="form-group">
                    <label for="message">Message *</label>
                    <textarea name="message" id="message" class="form-control" rows="6" required 
                              placeholder="Type your reply here..."></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="status">Update Status</label>
                        <select name="status" id="status" class="form-control">
                            <option value="">Keep current status</option>
                            <option value="open">Open</option>
                            <option value="in_progress">In Progress</option>
                            <option value="resolved">Resolved</option>
                            <option value="closed">Closed</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="is_internal" value="1">
                            <span class="checkmark"></span>
                            Internal Note (Not visible to customer)
                        </label>
                    </div>
                </div>
                
                <div class="form-actions">
                    <a href="' . base_url('admin/tickets') . '" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Tickets
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> Send Reply
                    </button>
                </div>
            </form>
        </div>
    </div>';

} else {
    $content .= '<div class="alert alert-danger">Ticket not found.</div>';
}

$content .= '<style>
.ticket-badges {
    display: flex;
    gap: 0.5rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.info-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
    font-weight: 500;
}

.info-value {
    color: white;
    font-weight: 600;
}

.ticket-messages {
    padding: 1.5rem;
    max-height: 600px;
    overflow-y: auto;
}

.message {
    margin-bottom: 1.5rem;
    padding: 1rem;
    border-radius: 12px;
    position: relative;
}

.user-message {
    background: rgba(59, 130, 246, 0.1);
    border-left: 4px solid #3b82f6;
    margin-right: 2rem;
}

.admin-message {
    background: rgba(16, 185, 129, 0.1);
    border-left: 4px solid #10b981;
    margin-left: 2rem;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.message-author {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    font-weight: 600;
}

.admin-badge {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
    padding: 0.125rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.message-time {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.875rem;
}

.message-content {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
}

.internal-note {
    margin-top: 0.75rem;
    padding: 0.5rem 0.75rem;
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: 6px;
    color: #f59e0b;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.empty-messages {
    text-align: center;
    padding: 3rem;
    color: rgba(255, 255, 255, 0.6);
}

.empty-messages i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 1.5rem;
    align-items: end;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    cursor: pointer;
    margin-bottom: 0;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}



.form-actions {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
}

.badge.secondary {
    background: rgba(156, 163, 175, 0.2);
    color: #9ca3af;
}
</style>';

$data['content'] = $content;
$this->load->view('admin/admin_template', $data);
?> 