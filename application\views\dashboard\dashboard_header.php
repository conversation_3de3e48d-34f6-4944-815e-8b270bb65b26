<!DOCTYPE html>
<html lang="id">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Dashboard - Dopminer</title>
	<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
	<link href="<?php echo secure_asset('assets/css/style.css'); ?>" rel="stylesheet">
	
	<style>
		/* Dashboard specific styles */
		.dashboard-content {
			min-height: 100vh;
			padding-top: 100px;
			padding-bottom: 2rem;
			opacity: 0;
			animation: fadeInContent 1s ease-out forwards;
		}
		
		@keyframes fadeInContent {
			from {
				opacity: 0;
				transform: translateY(20px);
			}
			to {
				opacity: 1;
				transform: translateY(0);
			}
		}
		
		.dashboard-container {
			max-width: 1400px;
			margin: 0 auto;
			padding: 2rem;
		}
		
		.dashboard-welcome {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 2rem;
		}
		
		.dashboard-welcome {
			opacity: 0;
			animation: slideInFromLeft 0.8s ease-out 0.2s forwards;
		}
		
		.dashboard-welcome h1 {
			font-size: 2rem;
			font-weight: 700;
			margin: 0;
		}
		
		@keyframes slideInFromLeft {
			from {
				opacity: 0;
				transform: translateX(-50px);
			}
			to {
				opacity: 1;
				transform: translateX(0);
			}
		}
		
		.dashboard-stats {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
			gap: 1.5rem;
			margin-bottom: 2rem;
		}
		
		.stat-card {
			background: rgba(255, 255, 255, 0.05);
			border: 1px solid rgba(255, 255, 255, 0.1);
			border-radius: 12px;
			padding: 1.5rem;
			backdrop-filter: blur(10px);
			transition: all 0.3s ease;
			opacity: 0;
			animation: slideInUp 0.6s ease-out forwards;
		}
		
		.stat-card:nth-child(1) { animation-delay: 0.4s; }
		.stat-card:nth-child(2) { animation-delay: 0.5s; }
		.stat-card:nth-child(3) { animation-delay: 0.6s; }
		.stat-card:nth-child(4) { animation-delay: 0.7s; }
		
		@keyframes slideInUp {
			from {
				opacity: 0;
				transform: translateY(30px) scale(0.95);
			}
			to {
				opacity: 1;
				transform: translateY(0) scale(1);
			}
		}
		
		.stat-card:hover {
			transform: translateY(-8px) scale(1.02);
			border-color: #0ea5e9;
			box-shadow: 0 15px 50px rgba(14, 165, 233, 0.3);
			background: rgba(255, 255, 255, 0.08);
		}
		
		.stat-icon {
			transition: all 0.3s ease;
		}
		
		.stat-card:hover .stat-icon {
			transform: scale(1.1) rotate(5deg);
		}
		
		.stat-card-header {
			display: flex;
			align-items: center;
			margin-bottom: 1rem;
		}
		
		.stat-icon {
			width: 50px;
			height: 50px;
			border-radius: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 1rem;
			font-size: 1.5rem;
		}
		
		.stat-icon.primary { background: rgba(14, 165, 233, 0.2); color: #0ea5e9; }
		.stat-icon.success { background: rgba(34, 197, 94, 0.2); color: #22c55e; }
		.stat-icon.info { background: rgba(168, 85, 247, 0.2); color: #a855f7; }
		.stat-icon.warning { background: rgba(245, 158, 11, 0.2); color: #f59e0b; }
		
		.stat-info h6 {
			color: rgba(255, 255, 255, 0.7);
			font-size: 0.875rem;
			margin: 0 0 0.5rem 0;
			font-weight: 500;
		}
		
		.stat-info h3 {
			color: white;
			font-size: 1.75rem;
			font-weight: 700;
			margin: 0;
		}
		
		.dashboard-section {
			background: rgba(255, 255, 255, 0.05);
			border: 1px solid rgba(255, 255, 255, 0.1);
			border-radius: 16px;
			margin-bottom: 2rem;
			backdrop-filter: blur(10px);
			overflow: hidden;
			opacity: 0;
			animation: fadeInSection 0.8s ease-out forwards;
		}
		
		.dashboard-section:nth-of-type(1) { animation-delay: 0.8s; }
		.dashboard-section:nth-of-type(2) { animation-delay: 1.0s; }
		.dashboard-section:nth-of-type(3) { animation-delay: 1.2s; }
		
		@keyframes fadeInSection {
			from {
				opacity: 0;
				transform: translateY(20px);
			}
			to {
				opacity: 1;
				transform: translateY(0);
			}
		}
		
		.section-header {
			padding: 1.5rem 2rem;
			border-bottom: 1px solid rgba(255, 255, 255, 0.1);
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		
		.section-header h5 {
			color: white;
			font-size: 1.25rem;
			font-weight: 600;
			margin: 0;
		}
		
		.section-link {
			color: #0ea5e9;
			text-decoration: none;
			font-size: 0.875rem;
			font-weight: 500;
			transition: color 0.3s ease;
		}
		
		.section-link:hover {
			color: #38bdf8;
		}
		
		.table-responsive {
			overflow-x: auto;
		}
		
		.dashboard-table {
			width: 100%;
			border-collapse: collapse;
		}
		
		.dashboard-table thead th {
			background: rgba(255, 255, 255, 0.05);
			color: rgba(255, 255, 255, 0.8);
			padding: 1rem 2rem;
			text-align: left;
			font-weight: 600;
			font-size: 0.875rem;
			border-bottom: 1px solid rgba(255, 255, 255, 0.1);
		}
		
		.dashboard-table tbody td {
			padding: 1rem 2rem;
			color: rgba(255, 255, 255, 0.9);
			border-bottom: 1px solid rgba(255, 255, 255, 0.05);
		}
		
		.dashboard-table tbody tr:hover {
			background: rgba(255, 255, 255, 0.02);
		}
		
		.badge {
			padding: 0.25rem 0.75rem;
			border-radius: 6px;
			font-size: 0.75rem;
			font-weight: 600;
			text-transform: uppercase;
		}
		
		.badge.primary { background: rgba(14, 165, 233, 0.2); color: #0ea5e9; }
		.badge.success { background: rgba(34, 197, 94, 0.2); color: #22c55e; }
		.badge.danger { background: rgba(239, 68, 68, 0.2); color: #ef4444; }
		.badge.warning { background: rgba(245, 158, 11, 0.2); color: #f59e0b; }
		.badge.info { background: rgba(168, 85, 247, 0.2); color: #a855f7; }
		
		.btn-sm {
			padding: 0.375rem 0.75rem;
			font-size: 0.875rem;
		}
		
		.empty-state {
			text-align: center;
			padding: 3rem 2rem;
			color: rgba(255, 255, 255, 0.6);
		}
		
		.empty-state i {
			font-size: 3rem;
			margin-bottom: 1rem;
			color: rgba(255, 255, 255, 0.3);
		}
		
		.alert {
			padding: 1rem 1.5rem;
			border-radius: 12px;
			margin-bottom: 1.5rem;
			border: 1px solid;
		}
		
		.alert-success {
			background: rgba(34, 197, 94, 0.1);
			border-color: rgba(34, 197, 94, 0.3);
			color: #22c55e;
		}
		
		.alert-danger {
			background: rgba(239, 68, 68, 0.1);
			border-color: rgba(239, 68, 68, 0.3);
			color: #ef4444;
		}
		
		/* Loading Animation */
		.loading-overlay {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: #0a0a0a;
			display: flex;
			align-items: center;
			justify-content: center;
			z-index: 9999;
			opacity: 1;
			transition: opacity 0.5s ease-out;
		}
		
		.loading-overlay.fade-out {
			opacity: 0;
			pointer-events: none;
		}
		
		.loading-spinner {
			width: 60px;
			height: 60px;
			border: 3px solid rgba(14, 165, 233, 0.3);
			border-top: 3px solid #0ea5e9;
			border-radius: 50%;
			animation: spin 1s linear infinite;
		}
		
		@keyframes spin {
			0% { transform: rotate(0deg); }
			100% { transform: rotate(360deg); }
		}
		
		/* Floating Particles */
		.dashboard-particles {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			pointer-events: none;
			z-index: 1;
		}
		
		.particle {
			position: absolute;
			width: 4px;
			height: 4px;
			background: rgba(14, 165, 233, 0.3);
			border-radius: 50%;
			animation: floatParticle 15s linear infinite;
		}
		
		.particle:nth-child(1) { left: 10%; animation-delay: 0s; }
		.particle:nth-child(2) { left: 20%; animation-delay: 2s; background: rgba(168, 85, 247, 0.3); }
		.particle:nth-child(3) { left: 30%; animation-delay: 4s; }
		.particle:nth-child(4) { left: 40%; animation-delay: 6s; background: rgba(34, 197, 94, 0.3); }
		.particle:nth-child(5) { left: 50%; animation-delay: 8s; }
		.particle:nth-child(6) { left: 60%; animation-delay: 1s; background: rgba(245, 158, 11, 0.3); }
		.particle:nth-child(7) { left: 70%; animation-delay: 3s; }
		.particle:nth-child(8) { left: 80%; animation-delay: 5s; background: rgba(239, 68, 68, 0.3); }
		
		@keyframes floatParticle {
			0% {
				transform: translateY(100vh) rotate(0deg);
				opacity: 0;
			}
			10% {
				opacity: 1;
			}
			90% {
				opacity: 1;
			}
			100% {
				transform: translateY(-100px) rotate(360deg);
				opacity: 0;
			}
		}
		
		/* Number Counter Animation */
		.counter {
			display: inline-block;
			font-weight: bold;
		}
		
		/* Welcome message animation */
		.dashboard-welcome h1 {
			background: linear-gradient(45deg, #0ea5e9, #8b5cf6, #06b6d4);
			background-size: 300% 300%;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			background-clip: text;
			animation: gradientShift 3s ease-in-out infinite;
		}
		
		@keyframes gradientShift {
			0%, 100% {
				background-position: 0% 50%;
			}
			50% {
				background-position: 100% 50%;
			}
		}
		
		/* Section headers animation */
		.section-header h5 {
			position: relative;
			display: inline-block;
		}
		
		.section-header h5::after {
			content: '';
			position: absolute;
			bottom: -2px;
			left: 0;
			width: 0;
			height: 2px;
			background: linear-gradient(90deg, #0ea5e9, #8b5cf6);
			transition: width 0.3s ease;
		}
		
		.dashboard-section:hover .section-header h5::after {
			width: 100%;
		}
		
		/* Progress bar for loading */
		.loading-progress {
			position: fixed;
			top: 0;
			left: 0;
			width: 0%;
			height: 3px;
			background: linear-gradient(90deg, #0ea5e9, #8b5cf6, #06b6d4);
			z-index: 10000;
			transition: width 0.3s ease;
		}
		
		/* Pulse effect for buttons */
		.btn:hover {
			animation: pulse 0.6s ease-in-out;
		}
		
		@keyframes pulse {
			0% {
				transform: scale(1);
			}
			50% {
				transform: scale(1.05);
			}
			100% {
				transform: scale(1);
			}
		}
		
		/* Glow effect for active elements */
		.stat-card.active {
			box-shadow: 0 0 30px rgba(14, 165, 233, 0.5);
			border-color: #0ea5e9;
		}
		
		@media (max-width: 768px) {
			.dashboard-container {
				padding: 1rem;
			}
			
			.dashboard-welcome {
				flex-direction: column;
				align-items: flex-start;
				gap: 1rem;
			}
			
			.dashboard-stats {
				grid-template-columns: 1fr;
			}
		}
	</style>
</head>
<body>
	<!-- Loading Progress Bar -->
	<div class="loading-progress" id="loadingProgress"></div>
	
	<!-- Loading Overlay -->
	<div class="loading-overlay" id="loadingOverlay">
		<div class="loading-spinner"></div>
	</div>
	
	<!-- Floating Particles -->
	<div class="dashboard-particles">
		<div class="particle"></div>
		<div class="particle"></div>
		<div class="particle"></div>
		<div class="particle"></div>
		<div class="particle"></div>
		<div class="particle"></div>
		<div class="particle"></div>
		<div class="particle"></div>
	</div>
	
	<?php $this->load->view('includes/header'); ?>
	
	<div class="dashboard-content">
		<div class="dashboard-container"> 