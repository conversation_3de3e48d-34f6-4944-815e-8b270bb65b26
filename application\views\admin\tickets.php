<?php
defined('BASEPATH') OR exit('No direct script access allowed');
// Use admin template with tickets content
$data['title'] = $title ?? 'Support Tickets';
$data['content'] = '
<div class="page-header">
    <h1>Support Tickets</h1>
    <p>Manage customer support tickets</p>
</div>';

// Show ticket statistics if available
if (isset($stats)):
    $data['content'] .= '
<div class="dashboard-stats" style="margin-bottom: 2rem;">
    <div class="stat-card">
        <div class="stat-icon" style="background: linear-gradient(135deg, #0ea5e9, #0284c7);">
            <i class="fas fa-ticket-alt"></i>
        </div>
        <div class="stat-info">
            <div class="stat-label">Open Tickets</div>
            <div class="stat-value">' . ($stats->open ?? 0) . '</div>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
            <i class="fas fa-clock"></i>
        </div>
        <div class="stat-info">
            <div class="stat-label">In Progress</div>
            <div class="stat-value">' . ($stats->in_progress ?? 0) . '</div>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon" style="background: linear-gradient(135deg, #22c55e, #16a34a);">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-info">
            <div class="stat-label">Resolved</div>
            <div class="stat-value">' . ($stats->resolved ?? 0) . '</div>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
            <i class="fas fa-times-circle"></i>
        </div>
        <div class="stat-info">
            <div class="stat-label">Closed</div>
            <div class="stat-value">' . ($stats->closed ?? 0) . '</div>
        </div>
    </div>
</div>';
endif;

$data['content'] .= '
<div class="admin-card">
    <div class="card-header">
        <h2><i class="fas fa-ticket-alt"></i> All Tickets</h2>
    </div>
    <div class="table-responsive">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>Ticket #</th>
                    <th>User</th>
                    <th>Subject</th>
                    <th>Category</th>
                    <th>Priority</th>
                    <th>Status</th>
                    <th>Last Update</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>';

if (isset($tickets) && !empty($tickets)):
    foreach ($tickets as $ticket):
        $data['content'] .= '
                <tr>
                    <td>#' . $ticket->ticket_number . '</td>
                    <td>' . ($ticket->username ?? 'N/A') . '</td>
                    <td>' . substr($ticket->subject, 0, 50) . (strlen($ticket->subject) > 50 ? '...' : '') . '</td>
                    <td><span class="badge info">' . ucfirst($ticket->category ?? 'general') . '</span></td>
                    <td>
                        <span class="badge ' . ($ticket->priority == 'high' ? 'danger' : ($ticket->priority == 'medium' ? 'warning' : 'info')) . '">
                            ' . ucfirst($ticket->priority) . '
                        </span>
                    </td>
                    <td>
                        <span class="badge ' . ($ticket->status == 'open' ? 'warning' : ($ticket->status == 'resolved' ? 'success' : 'info')) . '">
                            ' . ucfirst($ticket->status) . '
                        </span>
                    </td>
                    <td>' . date('d M Y H:i', strtotime($ticket->updated_at ?? $ticket->created_at)) . '</td>
                    <td>
                        <div class="action-buttons">
                            <a href="' . base_url('admin/ticket_details/' . $ticket->id) . '" class="btn btn-sm btn-primary">
                                <i class="fas fa-eye"></i> View
                            </a>
                        </div>
                    </td>
                </tr>';
    endforeach;
else:
    $data['content'] .= '
                <tr>
                    <td colspan="8" style="text-align: center; color: rgba(255, 255, 255, 0.5);">
                        No tickets found
                    </td>
                </tr>';
endif;

$data['content'] .= '
            </tbody>
        </table>
    </div>';

// Add pagination if available
if (isset($pagination) && !empty($pagination)):
    $data['content'] .= '
    <div style="padding: 1rem 2rem;">
        ' . $pagination . '
    </div>';
endif;

$data['content'] .= '</div>';

// Custom styles for ticket stats
$data['content'] .= '
<style>
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}
.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}
.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}
.stat-info {
    flex: 1;
}
.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}
.stat-value {
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
}
</style>';

// Load admin template with tickets content
$this->load->view('admin/admin_template', $data);
?> 