<div class="configure-container">
    <div class="container">
        <div class="configure-header">
            <a href="<?= base_url('order') ?>" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Browse
            </a>
            <h1 class="configure-title">Configure Your Server</h1>
            <p class="configure-subtitle">Customize your <?= $plan->name ?> server</p>
        </div>

        <div class="configure-content">
            <div class="configure-grid">
                <!-- Plan Details -->
                <div class="plan-details">
                    <div class="plan-card">
                        <div class="plan-header">
                            <div class="plan-category"><?= $plan->category_name ?></div>
                            <h2 class="plan-name"><?= $plan->name ?></h2>
                            <p class="plan-description"><?= $plan->description ?></p>
                        </div>

                        <div class="plan-specs">
                            <h3 class="specs-title">Server Specifications</h3>
                            <?php if ($plan->specifications): ?>
                                <div class="spec-item">
                                    <i class="fas fa-microchip"></i>
                                    <span class="spec-label">CPU:</span>
                                    <span class="spec-value"><?= $plan->specifications['cpu'] ?></span>
                                </div>
                                <div class="spec-item">
                                    <i class="fas fa-memory"></i>
                                    <span class="spec-label">RAM:</span>
                                    <span class="spec-value"><?= $plan->specifications['ram'] ?></span>
                                </div>
                                <div class="spec-item">
                                    <i class="fas fa-hdd"></i>
                                    <span class="spec-label">Storage:</span>
                                    <span class="spec-value"><?= $plan->specifications['storage'] ?></span>
                                </div>
                                <div class="spec-item">
                                    <i class="fas fa-network-wired"></i>
                                    <span class="spec-label">Bandwidth:</span>
                                    <span class="spec-value"><?= $plan->specifications['bandwidth'] ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($plan->max_slots): ?>
                                <div class="spec-item">
                                    <i class="fas fa-users"></i>
                                    <span class="spec-label">Max Players:</span>
                                    <span class="spec-value"><?= $plan->max_slots ?> slots</span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="plan-features">
                            <h3 class="features-title">Included Features</h3>
                            <?php if ($plan->features): ?>
                                <?php foreach ($plan->features as $feature): ?>
                                    <div class="feature-item">
                                        <i class="fas fa-check"></i>
                                        <span><?= $feature ?></span>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Configuration Form -->
                <div class="configure-form">
                    <form method="post" action="<?= base_url('order/checkout') ?>" id="configureForm">
                        <input type="hidden" name="plan_id" value="<?= $plan->id ?>">
                        
                        <div class="form-section">
                            <h3 class="section-title">Server Configuration</h3>
                            
                            <div class="form-group">
                                <label for="server_name" class="form-label">
                                    <i class="fas fa-server"></i>
                                    Server Name
                                </label>
                                <input type="text" 
                                       id="server_name" 
                                       name="server_name" 
                                       class="form-control" 
                                       placeholder="Enter your server name (e.g., MyAwesomeServer)"
                                       pattern="[a-zA-Z0-9_-]+"
                                       maxlength="50"
                                       required>
                                <small class="form-help">Only letters, numbers, underscores, and hyphens allowed. Max 50 characters.</small>
                            </div>

                            <div class="form-group">
                                <label for="billing_cycle" class="form-label">
                                    <i class="fas fa-calendar-alt"></i>
                                    Billing Cycle
                                </label>
                                <div class="billing-options">
                                    <div class="billing-option">
                                        <input type="radio" id="monthly" name="billing_cycle" value="monthly" checked>
                                        <label for="monthly" class="billing-label">
                                            <div class="billing-period">Monthly</div>
                                            <div class="billing-price">Rp <?= number_format($plan->price_monthly, 0, ',', '.') ?>/month</div>
                                            <div class="billing-total">Total: Rp <?= number_format($plan->price_monthly, 0, ',', '.') ?></div>
                                        </label>
                                    </div>

                                    <?php if ($plan->price_quarterly): ?>
                                    <div class="billing-option">
                                        <input type="radio" id="quarterly" name="billing_cycle" value="quarterly">
                                        <label for="quarterly" class="billing-label">
                                            <div class="billing-period">Quarterly</div>
                                            <div class="billing-price">Rp <?= number_format($plan->price_quarterly / 3, 0, ',', '.') ?>/month</div>
                                            <div class="billing-total">Total: Rp <?= number_format($plan->price_quarterly, 0, ',', '.') ?></div>
                                            <div class="billing-save">Save <?= round((($plan->price_monthly * 3) - $plan->price_quarterly) / ($plan->price_monthly * 3) * 100) ?>%</div>
                                        </label>
                                    </div>
                                    <?php endif; ?>

                                    <?php if ($plan->price_yearly): ?>
                                    <div class="billing-option">
                                        <input type="radio" id="yearly" name="billing_cycle" value="yearly">
                                        <label for="yearly" class="billing-label">
                                            <div class="billing-period">Yearly</div>
                                            <div class="billing-price">Rp <?= number_format($plan->price_yearly / 12, 0, ',', '.') ?>/month</div>
                                            <div class="billing-total">Total: Rp <?= number_format($plan->price_yearly, 0, ',', '.') ?></div>
                                            <div class="billing-save">Save <?= round((($plan->price_monthly * 12) - $plan->price_yearly) / ($plan->price_monthly * 12) * 100) ?>%</div>
                                        </label>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Order Summary -->
                        <div class="form-section">
                            <h3 class="section-title">Order Summary</h3>
                            <div class="order-summary">
                                <div class="summary-item">
                                    <span class="summary-label">Plan:</span>
                                    <span class="summary-value"><?= $plan->name ?></span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">Billing Cycle:</span>
                                    <span class="summary-value" id="selected-cycle">Monthly</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">Subtotal:</span>
                                    <span class="summary-value" id="subtotal">Rp <?= number_format($plan->price_monthly, 0, ',', '.') ?></span>
                                </div>
                                <?php if ($plan->setup_fee > 0): ?>
                                <div class="summary-item">
                                    <span class="summary-label">Setup Fee:</span>
                                    <span class="summary-value">Rp <?= number_format($plan->setup_fee, 0, ',', '.') ?></span>
                                </div>
                                <?php endif; ?>
                                <div class="summary-divider"></div>
                                <div class="summary-item summary-total">
                                    <span class="summary-label">Total:</span>
                                    <span class="summary-value" id="total">Rp <?= number_format($plan->price_monthly + $plan->setup_fee, 0, ',', '.') ?></span>
                                </div>
                                
                                <div class="balance-info">
                                    <div class="balance-item">
                                        <i class="fas fa-wallet"></i>
                                        <span>Your Balance: Rp <?= number_format($user->balance, 0, ',', '.') ?></span>
                                    </div>
                                    <?php if ($user->balance < ($plan->price_monthly + $plan->setup_fee)): ?>
                                        <div class="balance-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            <span>Insufficient balance. Please top up your account or choose bank transfer payment.</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <a href="<?= base_url('order') ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i>
                                Back
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-shopping-cart"></i>
                                Proceed to Checkout
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.configure-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 2rem 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.configure-header {
    text-align: center;
    margin-bottom: 3rem;
}

.back-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.back-btn:hover {
    color: #764ba2;
    transform: translateX(-4px);
}

.configure-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.configure-subtitle {
    font-size: 1.2rem;
    color: #718096;
}

.configure-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.plan-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    position: sticky;
    top: 2rem;
}

.plan-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #f7fafc;
}

.plan-category {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 1rem;
}

.plan-name {
    font-size: 1.75rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
}

.plan-description {
    color: #718096;
    line-height: 1.6;
}

.specs-title, .features-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
}

.spec-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    background: #f7fafc;
    border-radius: 8px;
}

.spec-item i {
    color: #667eea;
    width: 20px;
}

.spec-label {
    font-weight: 600;
    color: #4a5568;
    min-width: 80px;
}

.spec-value {
    color: #2d3748;
    font-weight: 500;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
    color: #4a5568;
}

.feature-item i {
    color: #48bb78;
    width: 16px;
}

.configure-form {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.form-section {
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f7fafc;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-help {
    color: #718096;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.billing-options {
    display: grid;
    gap: 1rem;
}

.billing-option {
    position: relative;
}

.billing-option input[type="radio"] {
    position: absolute;
    opacity: 0;
}

.billing-label {
    display: block;
    padding: 1.5rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.billing-option input[type="radio"]:checked + .billing-label {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
}

.billing-period {
    font-weight: 700;
    color: #2d3748;
    font-size: 1.1rem;
}

.billing-price {
    color: #667eea;
    font-weight: 600;
    margin: 0.25rem 0;
}

.billing-total {
    color: #4a5568;
    font-size: 0.875rem;
}

.billing-save {
    position: absolute;
    top: -8px;
    right: 1rem;
    background: #48bb78;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.order-summary {
    background: #f7fafc;
    border-radius: 12px;
    padding: 1.5rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.summary-label {
    color: #4a5568;
    font-weight: 500;
}

.summary-value {
    color: #2d3748;
    font-weight: 600;
}

.summary-divider {
    height: 1px;
    background: #e2e8f0;
    margin: 1rem 0;
}

.summary-total {
    font-size: 1.2rem;
    font-weight: 700;
}

.summary-total .summary-value {
    color: #667eea;
}

.balance-info {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

.balance-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #4a5568;
    margin-bottom: 0.5rem;
}

.balance-item i {
    color: #48bb78;
}

.balance-warning {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #e53e3e;
    font-size: 0.875rem;
    background: rgba(229, 62, 62, 0.1);
    padding: 0.75rem;
    border-radius: 8px;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: space-between;
    padding-top: 2rem;
    border-top: 2px solid #f7fafc;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
    transform: translateY(-2px);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

@media (max-width: 768px) {
    .configure-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .plan-card {
        position: static;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .configure-title {
        font-size: 2rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const billingOptions = document.querySelectorAll('input[name="billing_cycle"]');
    const subtotalEl = document.getElementById('subtotal');
    const totalEl = document.getElementById('total');
    const selectedCycleEl = document.getElementById('selected-cycle');
    
    const prices = {
        monthly: <?= $plan->price_monthly ?>,
        quarterly: <?= $plan->price_quarterly ?: 0 ?>,
        yearly: <?= $plan->price_yearly ?: 0 ?>
    };
    
    const setupFee = <?= $plan->setup_fee ?>;
    
    function updatePricing() {
        const selectedCycle = document.querySelector('input[name="billing_cycle"]:checked').value;
        const price = prices[selectedCycle];
        
        // Update display
        subtotalEl.textContent = 'Rp ' + price.toLocaleString('id-ID');
        totalEl.textContent = 'Rp ' + (price + setupFee).toLocaleString('id-ID');
        
        // Update cycle label
        const cycleLabels = {
            monthly: 'Monthly',
            quarterly: 'Quarterly (3 months)',
            yearly: 'Yearly (12 months)'
        };
        selectedCycleEl.textContent = cycleLabels[selectedCycle];
    }
    
    billingOptions.forEach(option => {
        option.addEventListener('change', updatePricing);
    });
    
    // Server name validation
    const serverNameInput = document.getElementById('server_name');
    serverNameInput.addEventListener('input', function() {
        this.value = this.value.replace(/[^a-zA-Z0-9_-]/g, '');
    });
    
    // Form validation
    document.getElementById('configureForm').addEventListener('submit', function(e) {
        const serverName = serverNameInput.value.trim();
        
        if (serverName.length < 3) {
            e.preventDefault();
            alert('Server name must be at least 3 characters long.');
            serverNameInput.focus();
            return;
        }
        
        if (!/^[a-zA-Z0-9_-]+$/.test(serverName)) {
            e.preventDefault();
            alert('Server name can only contain letters, numbers, underscores, and hyphens.');
            serverNameInput.focus();
            return;
        }
    });
    
    // Add animation to form elements
    const formElements = document.querySelectorAll('.form-section, .plan-card');
    formElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        element.style.transition = 'all 0.6s ease';
        
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 200);
    });
});
</script> 