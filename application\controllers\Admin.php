<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Admin extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('User_model');
        $this->load->model('Server_model');
        $this->load->model('Ticket_model');
        $this->load->library('session');
        
        // Check if user is logged in and is admin
        if (!$this->session->userdata('user_id')) {
            redirect('auth/login');
        }
        
        $user = $this->User_model->get_user($this->session->userdata('user_id'));
        if (!$user || $user->role !== 'admin') {
            $this->session->set_flashdata('error', 'Access denied. Admin privileges required.');
            redirect('dashboard');
        }
    }

    public function index() {
        $data['title'] = 'Admin Dashboard';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        
        // Get dashboard statistics
        $data['total_users'] = $this->User_model->count_total_users();
        $data['total_orders'] = count($this->Server_model->get_orders());
        $data['total_servers'] = count($this->Server_model->get_servers());
        $data['total_tickets'] = $this->Ticket_model->count_total_tickets();
        
        // Get recent activities
        $data['recent_orders'] = $this->Server_model->get_orders(null, 10);
        $data['recent_tickets'] = $this->Ticket_model->get_recent_tickets(10);
        $data['unassigned_tickets'] = $this->Ticket_model->get_unassigned_tickets();
        
        // Get revenue statistics
        $data['order_stats'] = $this->Server_model->get_order_stats();
        $data['popular_plans'] = $this->Server_model->get_popular_plans();
        
        $this->load->view('admin/dashboard', $data);
    }

    // ========== USER MANAGEMENT ==========
    public function users() {
        $data['title'] = 'Manage Users';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        
        // Pagination
        $this->load->library('pagination');
        $config['base_url'] = base_url('admin/users');
        $config['total_rows'] = $this->User_model->count_total_users();
        $config['per_page'] = 20;
        $config['uri_segment'] = 3;
        
        $this->pagination->initialize($config);
        
        $page = ($this->uri->segment(3)) ? $this->uri->segment(3) : 0;
        $data['users'] = $this->User_model->get_all_users($config['per_page'], $page);
        $data['pagination'] = $this->pagination->create_links();
        
        $this->load->view('admin/users', $data);
    }
    
    public function edit_user($user_id) {
        $data['title'] = 'Edit User';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        $data['edit_user'] = $this->User_model->get_user($user_id);
        
        if (!$data['edit_user']) {
            show_404();
        }
        
        if ($this->input->method() === 'post') {
            $this->form_validation->set_rules('username', 'Username', 'required|min_length[4]');
            $this->form_validation->set_rules('email', 'Email', 'required|valid_email');
            $this->form_validation->set_rules('role', 'Role', 'required|in_list[user,admin]');
            $this->form_validation->set_rules('status', 'Status', 'required|in_list[active,inactive,suspended]');
            
            if ($this->form_validation->run()) {
                $update_data = [
                    'username' => $this->input->post('username'),
                    'email' => $this->input->post('email'),
                    'full_name' => $this->input->post('full_name'),
                    'role' => $this->input->post('role'),
                    'status' => $this->input->post('status')
                ];
                
                // Update password if provided
                if ($this->input->post('password')) {
                    $update_data['password'] = password_hash($this->input->post('password'), PASSWORD_DEFAULT);
                }
                
                $this->db->where('id', $user_id);
                $this->db->update('users', $update_data);
                
                $this->session->set_flashdata('success', 'User updated successfully!');
                redirect('admin/users');
            }
        }
        
        $this->load->view('admin/edit_user', $data);
    }
    
    public function delete_user($user_id) {
        // Prevent deleting own account
        if ($user_id == $this->session->userdata('user_id')) {
            $this->session->set_flashdata('error', 'You cannot delete your own account!');
            redirect('admin/users');
        }
        
        $this->db->where('id', $user_id);
        $this->db->delete('users');
        
        $this->session->set_flashdata('success', 'User deleted successfully!');
        redirect('admin/users');
    }

    public function add_user() {
        $data['title'] = 'Add User';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        
        if ($this->input->method() === 'post') {
            $this->load->library('form_validation');
            $this->form_validation->set_rules('username', 'Username', 'required|min_length[4]|is_unique[users.username]');
            $this->form_validation->set_rules('email', 'Email', 'required|valid_email|is_unique[users.email]');
            $this->form_validation->set_rules('password', 'Password', 'required|min_length[6]');
            $this->form_validation->set_rules('role', 'Role', 'required|in_list[user,admin]');
            $this->form_validation->set_rules('status', 'Status', 'required|in_list[active,inactive,suspended]');
            
            if ($this->form_validation->run()) {
                $user_data = [
                    'username' => $this->input->post('username'),
                    'email' => $this->input->post('email'),
                    'full_name' => $this->input->post('full_name'),
                    'password' => password_hash($this->input->post('password'), PASSWORD_DEFAULT),
                    'role' => $this->input->post('role'),
                    'status' => $this->input->post('status'),
                    'balance' => $this->input->post('balance') ?: 0,
                    'phone' => $this->input->post('phone'),
                    'created_at' => date('Y-m-d H:i:s')
                ];
                
                $this->db->insert('users', $user_data);
                
                $this->session->set_flashdata('success', 'User created successfully!');
                redirect('admin/users');
            }
        }
        
        $this->load->view('admin/add_user', $data);
    }

    // ========== SERVER MANAGEMENT ==========
    public function server_plans() {
        $data['title'] = 'Server Plans';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        
        $data['plans'] = $this->Server_model->get_all_plans();
        $data['categories'] = $this->Server_model->get_categories();
        
        $this->load->view('admin/server_plans', $data);
    }

    public function add_plan() {
        $data['title'] = 'Add Server Plan';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        $data['categories'] = $this->Server_model->get_categories();
        
        if ($this->input->method() === 'post') {
            $this->form_validation->set_rules('category_id', 'Category', 'required|numeric');
            $this->form_validation->set_rules('name', 'Plan Name', 'required|min_length[3]|max_length[100]');
            $this->form_validation->set_rules('slug', 'Slug', 'required|alpha_dash|is_unique[server_plans.slug]');
            $this->form_validation->set_rules('price_monthly', 'Monthly Price', 'required|numeric');
            
            if ($this->form_validation->run()) {
                // Prepare specifications JSON
                $specifications = [
                    'cpu' => $this->input->post('cpu'),
                    'ram' => $this->input->post('ram'),
                    'storage' => $this->input->post('storage'),
                    'bandwidth' => $this->input->post('bandwidth')
                ];
                
                // Prepare features JSON
                $features_input = $this->input->post('features');
                $features = [];
                if ($features_input) {
                    $features = array_filter(explode("\n", $features_input));
                }
                
                $plan_data = [
                    'category_id' => $this->input->post('category_id'),
                    'name' => $this->input->post('name'),
                    'slug' => $this->input->post('slug'),
                    'description' => $this->input->post('description'),
                    'specifications' => json_encode($specifications),
                    'price_monthly' => $this->input->post('price_monthly'),
                    'price_quarterly' => $this->input->post('price_quarterly'),
                    'price_yearly' => $this->input->post('price_yearly'),
                    'setup_fee' => $this->input->post('setup_fee') ?: 0,
                    'max_slots' => $this->input->post('max_slots') ?: null,
                    'features' => json_encode($features),
                    'sort_order' => $this->input->post('sort_order') ?: 0,
                    'status' => 'active'
                ];
                
                $this->db->insert('server_plans', $plan_data);
                
                $this->session->set_flashdata('success', 'Server plan added successfully!');
                redirect('admin/server_plans');
            }
        }
        
        $this->load->view('admin/add_plan', $data);
    }
    
    public function edit_plan($plan_id) {
        $data['title'] = 'Edit Server Plan';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        $data['categories'] = $this->Server_model->get_categories();
        $data['plan'] = $this->Server_model->get_plan($plan_id);
        
        if (!$data['plan']) {
            show_404();
        }
        
        if ($this->input->method() === 'post') {
            $this->form_validation->set_rules('category_id', 'Category', 'required|numeric');
            $this->form_validation->set_rules('name', 'Plan Name', 'required|min_length[3]|max_length[100]');
            $this->form_validation->set_rules('price_monthly', 'Monthly Price', 'required|numeric');
            
            if ($this->form_validation->run()) {
                // Prepare specifications JSON
                $specifications = [
                    'cpu' => $this->input->post('cpu'),
                    'ram' => $this->input->post('ram'),
                    'storage' => $this->input->post('storage'),
                    'bandwidth' => $this->input->post('bandwidth')
                ];
                
                // Prepare features JSON
                $features_input = $this->input->post('features');
                $features = [];
                if ($features_input) {
                    $features = array_filter(explode("\n", $features_input));
                }
                
                $plan_data = [
                    'category_id' => $this->input->post('category_id'),
                    'name' => $this->input->post('name'),
                    'description' => $this->input->post('description'),
                    'specifications' => json_encode($specifications),
                    'price_monthly' => $this->input->post('price_monthly'),
                    'price_quarterly' => $this->input->post('price_quarterly'),
                    'price_yearly' => $this->input->post('price_yearly'),
                    'setup_fee' => $this->input->post('setup_fee') ?: 0,
                    'max_slots' => $this->input->post('max_slots') ?: null,
                    'features' => json_encode($features),
                    'sort_order' => $this->input->post('sort_order') ?: 0,
                    'status' => $this->input->post('status')
                ];
                
                $this->db->where('id', $plan_id);
                $this->db->update('server_plans', $plan_data);
                
                $this->session->set_flashdata('success', 'Server plan updated successfully!');
                redirect('admin/server_plans');
            }
        }
        
        $this->load->view('admin/edit_plan', $data);
    }
    
    public function delete_plan($plan_id) {
        $this->db->where('id', $plan_id);
        $this->db->delete('server_plans');
        
        $this->session->set_flashdata('success', 'Server plan deleted successfully!');
        redirect('admin/server_plans');
    }

    // ========== ORDER MANAGEMENT ==========
    public function orders() {
        $data['title'] = 'Order Management';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        
        // Get orders with user and plan information
        $this->db->select('orders.*, users.username, users.email, server_plans.name as plan_name');
        $this->db->from('orders');
        $this->db->join('users', 'users.id = orders.user_id', 'left');
        $this->db->join('server_plans', 'server_plans.id = orders.plan_id', 'left');
        $this->db->order_by('orders.created_at', 'DESC');
        $data['orders'] = $this->db->get()->result();
        
        $this->load->view('admin/orders', $data);
    }
    
    public function order_details($order_id) {
        $data['title'] = 'Order Details';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        
        // Get order with full details
        $this->db->select('orders.*, users.username, users.email, users.full_name, server_plans.name as plan_name, server_plans.specifications');
        $this->db->from('orders');
        $this->db->join('users', 'users.id = orders.user_id', 'left');
        $this->db->join('server_plans', 'server_plans.id = orders.plan_id', 'left');
        $this->db->where('orders.id', $order_id);
        $data['order'] = $this->db->get()->row();
        
        if (!$data['order']) {
            show_404();
        }
        
        // Handle status update
        if ($this->input->method() === 'post') {
            $update_data = [];
            
            if ($this->input->post('payment_status')) {
                $update_data['payment_status'] = $this->input->post('payment_status');
            }
            
            if ($this->input->post('server_status')) {
                $update_data['server_status'] = $this->input->post('server_status');
            }
            
            if ($this->input->post('admin_notes') !== null) {
                $update_data['admin_notes'] = $this->input->post('admin_notes');
            }
            
            $update_data['updated_at'] = date('Y-m-d H:i:s');
            
            $this->db->where('id', $order_id);
            $this->db->update('orders', $update_data);
            
            $this->session->set_flashdata('success', 'Order updated successfully!');
            redirect('admin/order_details/' . $order_id);
        }
        
        $this->load->view('admin/order_details', $data);
    }

    // ========== TICKET MANAGEMENT ==========
    public function tickets() {
        $data['title'] = 'Ticket Management';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        
        // Pagination
        $this->load->library('pagination');
        $config['base_url'] = base_url('admin/tickets');
        $config['total_rows'] = $this->Ticket_model->count_total_tickets();
        $config['per_page'] = 20;
        $config['uri_segment'] = 3;
        
        $this->pagination->initialize($config);
        
        $page = ($this->uri->segment(3)) ? $this->uri->segment(3) : 0;
        $data['tickets'] = $this->Ticket_model->get_tickets(null, null, $config['per_page'], $page);
        $data['pagination'] = $this->pagination->create_links();
        
        // Get ticket statistics
        $data['stats'] = $this->Ticket_model->get_ticket_stats();
        
        $this->load->view('admin/tickets', $data);
    }

    public function ticket_details($ticket_id) {
        $ticket = $this->Ticket_model->get_ticket($ticket_id);
        if (!$ticket) {
            show_404();
        }
        
        $data['title'] = 'Ticket Details - #' . $ticket->ticket_number;
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        $data['ticket'] = $ticket;
        $data['messages'] = $this->Ticket_model->get_all_messages($ticket_id);
        
        // Handle admin reply
        if ($this->input->method() === 'post') {
            $this->form_validation->set_rules('message', 'Message', 'required|min_length[5]');
            
            if ($this->form_validation->run()) {
                $message_data = [
                    'ticket_id' => $ticket_id,
                    'user_id' => $this->session->userdata('user_id'),
                    'message' => $this->input->post('message'),
                    'is_internal' => $this->input->post('is_internal') ? 1 : 0
                ];
                
                if ($this->Ticket_model->add_message($message_data)) {
                    // Update ticket status if provided
                    if ($this->input->post('status')) {
                        $this->Ticket_model->update_ticket($ticket_id, ['status' => $this->input->post('status')]);
                    }
                    
                    $this->session->set_flashdata('success', 'Reply added successfully!');
                    redirect('admin/ticket_details/' . $ticket_id);
                } else {
                    $this->session->set_flashdata('error', 'Failed to add reply. Please try again.');
                }
            }
        }
        
        $this->load->view('admin/ticket_details', $data);
    }
    
    // ========== TRANSACTION MANAGEMENT ==========
    public function transactions() {
        $data['title'] = 'Transaction Management';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        
        // Get transactions with user information
        $this->db->select('transactions.*, users.username, users.email');
        $this->db->from('transactions');
        $this->db->join('users', 'users.id = transactions.user_id', 'left');
        $this->db->order_by('transactions.created_at', 'DESC');
        $data['transactions'] = $this->db->get()->result();
        
        // Calculate totals
        $data['total_revenue'] = $this->db->select_sum('amount')->where('status', 'completed')->get('transactions')->row()->amount ?: 0;
        $data['pending_revenue'] = $this->db->select_sum('amount')->where('status', 'pending')->get('transactions')->row()->amount ?: 0;
        
        $this->load->view('admin/transactions', $data);
    }
    
    // ========== CATEGORY MANAGEMENT ==========
    public function categories() {
        $data['title'] = 'Category Management';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        
        // Get all categories with plan count
        $this->db->select('sc.*, COUNT(sp.id) as plan_count');
        $this->db->from('server_categories sc');
        $this->db->join('server_plans sp', 'sp.category_id = sc.id', 'left');
        $this->db->group_by('sc.id');
        $this->db->order_by('sc.sort_order', 'ASC');
        $data['categories'] = $this->db->get()->result();
        
        $this->load->view('admin/categories', $data);
    }
    
    public function add_category() {
        $data['title'] = 'Add Category';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        
        if ($this->input->method() === 'post') {
            $this->form_validation->set_rules('name', 'Category Name', 'required|min_length[3]|max_length[100]');
            $this->form_validation->set_rules('type', 'Category Type', 'required');
            
            if ($this->form_validation->run()) {
                // Generate slug from name
                $slug = url_title($this->input->post('name'), 'dash', TRUE);
                
                // Check if slug exists
                $existing = $this->db->get_where('server_categories', ['slug' => $slug])->row();
                if ($existing) {
                    $slug .= '-' . time();
                }
                
                $category_data = [
                    'name' => $this->input->post('name'),
                    'slug' => $slug,
                    'type' => $this->input->post('type'),
                    'description' => $this->input->post('description'),
                    'icon' => $this->input->post('icon'),
                    'sort_order' => $this->input->post('sort_order') ?: 0,
                    'status' => $this->input->post('status'),
                    'created_at' => date('Y-m-d H:i:s')
                ];
                
                $this->db->insert('server_categories', $category_data);
                
                $this->session->set_flashdata('success', 'Category created successfully!');
                redirect('admin/categories');
            }
        }
        
        $this->load->view('admin/add_category', $data);
    }
    
    public function edit_category($category_id) {
        $data['title'] = 'Edit Category';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        $data['category'] = $this->Server_model->get_category($category_id);
        
        if (!$data['category']) {
            show_404();
        }
        
        if ($this->input->method() === 'post') {
            $this->form_validation->set_rules('name', 'Category Name', 'required|min_length[3]|max_length[100]');
            $this->form_validation->set_rules('type', 'Category Type', 'required');
            
            if ($this->form_validation->run()) {
                $category_data = [
                    'name' => $this->input->post('name'),
                    'type' => $this->input->post('type'),
                    'description' => $this->input->post('description'),
                    'icon' => $this->input->post('icon'),
                    'sort_order' => $this->input->post('sort_order') ?: 0,
                    'status' => $this->input->post('status'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                $this->db->where('id', $category_id);
                $this->db->update('server_categories', $category_data);
                
                $this->session->set_flashdata('success', 'Category updated successfully!');
                redirect('admin/categories');
            }
        }
        
        $this->load->view('admin/edit_category', $data);
    }
    
    public function delete_category($category_id) {
        // Check if category has plans
        $plan_count = $this->db->where('category_id', $category_id)->count_all_results('server_plans');
        
        if ($plan_count > 0) {
            $this->session->set_flashdata('error', 'Cannot delete category with existing plans. Please move or delete plans first.');
        } else {
            $this->db->where('id', $category_id);
            $this->db->delete('server_categories');
            
            $this->session->set_flashdata('success', 'Category deleted successfully!');
        }
        
        redirect('admin/categories');
    }

    // ========== SETTINGS ==========
    public function settings() {
        $data['title'] = 'Settings';
        $data['user'] = $this->User_model->get_user($this->session->userdata('user_id'));
        
        // Get current settings
        $data['settings'] = $this->db->get('settings')->result();
        
        if ($this->input->method() === 'post') {
            // Update settings
            foreach ($this->input->post() as $key => $value) {
                if ($key !== 'submit') {
                    $this->db->where('key', $key);
                    $this->db->update('settings', ['value' => $value]);
                }
            }
            
            $this->session->set_flashdata('success', 'Settings updated successfully!');
            redirect('admin/settings');
        }
        
        $this->load->view('admin/settings', $data);
    }
} 