/*!
 * <PERSON><PERSON>iner Authentication Pages - Custom Styles
 * Copyright (c) 2024 Dopminer - All Rights Reserved
 */

/* <PERSON><PERSON> */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 8px;
    font-size: 14px;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

/* Auth Section */
.auth-section {
    min-height: 100vh;
    background: 
        linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.98) 100%),
        radial-gradient(circle at 20% 50%, rgba(255, 107, 0, 0.12) 0%, transparent 45%),
        radial-gradient(circle at 80% 20%, rgba(220, 38, 38, 0.10) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(14, 165, 233, 0.15) 0%, transparent 40%),
        radial-gradient(circle at 60% 30%, rgba(168, 85, 247, 0.06) 0%, transparent 55%);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    padding: 2rem 0;
    padding-top: 100px;
}

.auth-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(rgba(255, 107, 0, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(220, 38, 38, 0.04) 1px, transparent 1px),
        linear-gradient(45deg, rgba(14, 165, 233, 0.03) 1px, transparent 1px);
    background-size: 80px 80px, 60px 60px, 120px 120px;
    z-index: 1;
    animation: authGridMove 60s linear infinite;
}

@keyframes authGridMove {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(40px, 40px);
    }
}

.auth-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 2;
    width: 100%;
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

/* Auth Card */
.auth-card {
    background: 
        linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.85) 100%);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 20px;
    padding: 3rem;
    backdrop-filter: blur(20px);
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
    max-width: 500px;
    margin: 0 auto;
    animation: fadeInScale 0.6s ease-out forwards;
    opacity: 0;
    animation-delay: 0.2s;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-blue), transparent);
}

.auth-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.auth-logo {
    width: 60px;
    height: 60px;
    margin: 0 auto 1.5rem;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 30px rgba(14, 165, 233, 0.3);
}

.auth-logo img {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.auth-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 0.75rem;
}

.auth-header p {
    color: var(--gray-400);
    font-size: 1rem;
    line-height: 1.6;
}

/* Form Styles */
.auth-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
    animation: fadeIn 0.4s ease-out forwards;
    opacity: 0;
}

.form-group:nth-child(1) { animation-delay: 0.3s; }
.form-group:nth-child(2) { animation-delay: 0.4s; }
.form-group:nth-child(3) { animation-delay: 0.5s; }
.form-group:nth-child(4) { animation-delay: 0.6s; }

.form-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--gray-300);
    font-weight: 500;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

.form-group label i {
    color: var(--primary-blue);
    font-size: 0.9rem;
}

.form-group input {
    width: 100%;
    padding: 1rem 1.25rem;
    background: rgba(30, 41, 59, 0.7);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 12px;
    color: var(--white);
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
    background: rgba(30, 41, 59, 0.9);
}

.form-group input::placeholder {
    color: var(--gray-500);
}

.password-field {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    transition: color 0.3s ease;
    padding: 0.5rem;
}

.password-toggle:hover {
    color: var(--primary-blue);
}

.password-strength {
    margin-top: 0.75rem;
}

.strength-bar {
    height: 4px;
    background: var(--gray-700);
    border-radius: 2px;
    position: relative;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.strength-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-text {
    font-size: 0.8rem;
    color: var(--gray-500);
    font-weight: 500;
}

/* Form Options */
.form-options {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.checkbox-wrapper {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    color: var(--gray-400);
    font-size: 0.9rem;
    line-height: 1.5;
    flex: 1;
}

.checkbox-wrapper input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    background: rgba(30, 41, 59, 0.7);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 3px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.forgot-link, .terms-link {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.forgot-link:hover, .terms-link:hover {
    color: var(--secondary-blue);
}

/* Auth Button */
.auth-btn {
    width: 100%;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    box-shadow: 0 4px 15px rgba(14, 165, 233, 0.3);
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
    animation-delay: 0.7s;
}

.auth-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.15),
        transparent
    );
    transition: left 0.6s ease;
}

.auth-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(14, 165, 233, 0.4);
}

.auth-btn:hover::before {
    left: 100%;
}

.auth-btn:active {
    transform: translateY(0);
}

/* Divider */
.auth-divider {
    display: flex;
    align-items: center;
    margin: 2rem 0;
    color: var(--gray-500);
    font-size: 0.9rem;
}

.auth-divider::before,
.auth-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: rgba(148, 163, 184, 0.2);
}

.auth-divider span {
    padding: 0 1rem;
}

/* Social Login */
.social-login {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
    animation-delay: 0.8s;
}

.social-btn {
    padding: 0.875rem 1rem;
    background: rgba(30, 41, 59, 0.7);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 12px;
    color: var(--gray-300);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
}

.social-btn:hover {
    background: rgba(30, 41, 59, 0.9);
    border-color: var(--primary-blue);
    transform: translateY(-2px);
}

.social-btn.google:hover {
    border-color: #ea4335;
    color: #ea4335;
}

.social-btn.discord:hover {
    border-color: #5865F2;
    color: #5865F2;
}

/* Auth Footer */
.auth-footer {
    text-align: center;
    color: var(--gray-400);
    font-size: 0.9rem;
}

.auth-footer a {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.auth-footer a:hover {
    color: var(--secondary-blue);
}

/* Auth Info */
.auth-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.info-card {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(148, 163, 184, 0.1);
    border-radius: 16px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    animation: fadeInRight 0.6s ease-out forwards;
    opacity: 0;
}

.info-card:nth-child(1) { animation-delay: 0.4s; }
.info-card:nth-child(2) { animation-delay: 0.6s; }
.info-card:nth-child(3) { animation-delay: 0.8s; }

.info-card:hover {
    transform: translateY(-5px);
    border-color: rgba(14, 165, 233, 0.3);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.info-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    box-shadow: 0 8px 20px rgba(14, 165, 233, 0.25);
}

.info-icon i {
    font-size: 1.25rem;
    color: var(--white);
}

.info-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--white);
    margin-bottom: 0.75rem;
}

.info-card p {
    color: var(--gray-400);
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .auth-container {
        grid-template-columns: 1fr;
        gap: 3rem;
        max-width: 600px;
    }
    
    .auth-info {
        order: -1;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .auth-section {
        padding: 1rem 0;
        padding-top: 80px;
    }
    
    .auth-container {
        padding: 0 1rem;
        gap: 2rem;
    }
    
    .auth-card {
        padding: 2rem;
        border-radius: 16px;
    }
    
    .auth-header h1 {
        font-size: 1.75rem;
    }
    
    .social-login {
        grid-template-columns: 1fr;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .auth-info {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .auth-card {
        padding: 1.5rem;
    }
    
    .auth-header h1 {
        font-size: 1.5rem;
    }
    
    .info-card {
        padding: 1.5rem;
    }
}

.agreement-wrapper {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    margin: 15px 0;
    padding-left: 4px;
}

.agreement-wrapper input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin: 2px 0 0 0;
    cursor: pointer;
    flex-shrink: 0;
}

.agreement-wrapper label {
    font-size: 13px;
    line-height: 20px;
    color: #fff;
    cursor: pointer;
    user-select: none;
    display: inline-block;
    padding-top: 1px;
}

.agreement-wrapper .link {
    color: #00a8ff;
    text-decoration: none;
    transition: color 0.2s;
}

.agreement-wrapper .link:hover {
    color: #0097e6;
    text-decoration: underline;
}

/* Animasi untuk auth container */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
} 