<!-- Servers Section -->
<div class="dashboard-welcome">
    <h1>My Servers</h1>
    <a href="#" class="btn btn-primary">
        <i class="fas fa-plus"></i> Order New Server
    </a>
</div>

<!-- Servers List -->
<div class="dashboard-section">
    <div class="section-header">
        <h5>Server Management</h5>
    </div>
    <div class="table-responsive">
        <table class="dashboard-table">
            <thead>
                <tr>
                    <th>Server Name</th>
                    <th>Type</th>
                    <th>IP:Port</th>
                    <th>Resources</th>
                    <th>Status</th>
                    <th>Expiry Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($servers)): ?>
                <tr>
                    <td colspan="7">
                        <div class="empty-state">
                            <i class="fas fa-server"></i>
                            <div>You don't have any servers yet</div>
                            <a href="#" class="btn btn-primary" style="margin-top: 1rem;">
                                <i class="fas fa-plus"></i> Order Your First Server
                            </a>
                        </div>
                    </td>
                </tr>
                <?php else: ?>
                    <?php foreach ($servers as $server): ?>
                    <tr>
                        <td>
                            <div style="font-weight: 600; color: white;"><?= $server->server_name ?></div>
                            <div style="font-size: 0.875rem; color: rgba(255, 255, 255, 0.6);">ID: #<?= $server->id ?></div>
                        </td>
                        <td>
                            <span class="badge <?= $server->server_type == 'fivem' ? 'primary' : ($server->server_type == 'samp' ? 'success' : 'danger') ?>">
                                <?= strtoupper($server->server_type) ?>
                            </span>
                        </td>
                        <td>
                            <div style="font-family: 'JetBrains Mono', monospace; color: #0ea5e9;">
                                <?= $server->server_ip ?>:<?= $server->server_port ?>
                            </div>
                        </td>
                        <td>
                            <div style="font-size: 0.875rem;">
                                <div>CPU: <?= $server->cpu ?>%</div>
                                <div>RAM: <?= $server->ram ?>GB</div>
                                <div>Disk: <?= $server->disk ?>GB</div>
                            </div>
                        </td>
                        <td>
                            <span class="badge <?= $server->status == 'active' ? 'success' : ($server->status == 'suspended' ? 'warning' : 'danger') ?>">
                                <?= ucfirst($server->status) ?>
                            </span>
                        </td>
                        <td>
                            <div style="color: white;"><?= date('d M Y', strtotime($server->expiry_date)) ?></div>
                            <div style="font-size: 0.875rem; color: rgba(255, 255, 255, 0.6);">
                                <?php 
                                $days_left = ceil((strtotime($server->expiry_date) - time()) / (60 * 60 * 24));
                                echo $days_left > 0 ? $days_left . ' days left' : 'Expired';
                                ?>
                            </div>
                        </td>
                        <td>
                            <div style="display: flex; gap: 0.5rem;">
                                <a href="#" class="btn btn-primary btn-sm" title="Manage">
                                    <i class="fas fa-cog"></i>
                                </a>
                                <a href="#" class="btn btn-success btn-sm" title="Restart">
                                    <i class="fas fa-redo"></i>
                                </a>
                                <a href="#" class="btn btn-warning btn-sm" title="Renew">
                                    <i class="fas fa-calendar-plus"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div> 