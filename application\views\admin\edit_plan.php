<?php
defined('BASEPATH') OR exit('No direct script access allowed');
$data['title'] = $title ?? 'Edit Server Plan';

$content = '<div class="page-header">
    <h1>Edit Server Plan</h1>
    <p>Update server hosting plan information</p>
</div>';

// Handle form submission
if ($this->input->method() === 'post') {
    if (validation_errors()) {
        $content .= '<div class="alert alert-danger">' . validation_errors() . '</div>';
    }
    
    if ($this->session->flashdata('success')) {
        $content .= '<div class="alert alert-success">' . $this->session->flashdata('success') . '</div>';
    }
    
    if ($this->session->flashdata('error')) {
        $content .= '<div class="alert alert-danger">' . $this->session->flashdata('error') . '</div>';
    }
}

if (isset($plan) && $plan) {
    // Safely decode JSON fields for form
    $specs = [];
    if (isset($plan->specifications) && !empty($plan->specifications)) {
        if (is_array($plan->specifications)) {
            $specs = $plan->specifications;
        } elseif (is_string($plan->specifications)) {
            $decoded = @json_decode($plan->specifications, true);
            if (is_array($decoded)) {
                $specs = $decoded;
            }
        }
    }
    
    $features = [];
    if (isset($plan->features) && !empty($plan->features)) {
        if (is_array($plan->features)) {
            $features = $plan->features;
        } elseif (is_string($plan->features)) {
            $decoded = @json_decode($plan->features, true);
            if (is_array($decoded)) {
                $features = $decoded;
            }
        }
    }
    
    $content .= '<form method="post" action="' . base_url('admin/edit_plan/' . $plan->id) . '">
        <div class="admin-card">
            <div class="card-header">
                <h2><i class="fas fa-server"></i> Plan Information</h2>
            </div>
            <div style="padding: 2rem;">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="category_id">Category *</label>
                        <select name="category_id" id="category_id" class="form-control" required>';

    if (isset($categories) && !empty($categories)) {
        foreach ($categories as $category) {
            $selected = set_select('category_id', $category->id, $plan->category_id == $category->id);
            $content .= '<option value="' . $category->id . '" ' . $selected . '>' . $category->name . '</option>';
        }
    }

    $content .= '</select>
                    </div>
                    
                    <div class="form-group">
                        <label for="name">Plan Name *</label>
                        <input type="text" name="name" id="name" class="form-control" 
                               value="' . set_value('name', $plan->name) . '" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="slug">Slug</label>
                        <input type="text" name="slug" id="slug" class="form-control" 
                               value="' . set_value('slug', $plan->slug) . '" readonly>
                        <small class="form-text">Slug cannot be changed after creation</small>
                    </div>
                    
                    <div class="form-group full-width">
                        <label for="description">Description</label>
                        <textarea name="description" id="description" class="form-control" rows="3">' . set_value('description', $plan->description ?? '') . '</textarea>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="admin-card">
            <div class="card-header">
                <h2><i class="fas fa-cog"></i> Specifications</h2>
            </div>
            <div style="padding: 2rem;">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="cpu">CPU</label>
                        <input type="text" name="cpu" id="cpu" class="form-control" 
                               value="' . set_value('cpu', $specs['cpu'] ?? '') . '" placeholder="e.g., 2 vCPU">
                    </div>
                    
                    <div class="form-group">
                        <label for="ram">RAM</label>
                        <input type="text" name="ram" id="ram" class="form-control" 
                               value="' . set_value('ram', $specs['ram'] ?? '') . '" placeholder="e.g., 4 GB">
                    </div>
                    
                    <div class="form-group">
                        <label for="storage">Storage</label>
                        <input type="text" name="storage" id="storage" class="form-control" 
                               value="' . set_value('storage', $specs['storage'] ?? '') . '" placeholder="e.g., 50 GB SSD">
                    </div>
                    
                    <div class="form-group">
                        <label for="bandwidth">Bandwidth</label>
                        <input type="text" name="bandwidth" id="bandwidth" class="form-control" 
                               value="' . set_value('bandwidth', $specs['bandwidth'] ?? '') . '" placeholder="e.g., Unlimited">
                    </div>
                    
                    <div class="form-group">
                        <label for="max_slots">Max Slots</label>
                        <input type="number" name="max_slots" id="max_slots" class="form-control" 
                               value="' . set_value('max_slots', $plan->max_slots ?? '') . '" min="1">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="admin-card">
            <div class="card-header">
                <h2><i class="fas fa-dollar-sign"></i> Pricing</h2>
            </div>
            <div style="padding: 2rem;">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="price_monthly">Monthly Price (IDR) *</label>
                        <input type="number" name="price_monthly" id="price_monthly" class="form-control" 
                               value="' . set_value('price_monthly', $plan->price_monthly) . '" min="0" step="1000" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="price_quarterly">Quarterly Price (IDR)</label>
                        <input type="number" name="price_quarterly" id="price_quarterly" class="form-control" 
                               value="' . set_value('price_quarterly', $plan->price_quarterly ?? '') . '" min="0" step="1000">
                    </div>
                    
                    <div class="form-group">
                        <label for="price_yearly">Yearly Price (IDR)</label>
                        <input type="number" name="price_yearly" id="price_yearly" class="form-control" 
                               value="' . set_value('price_yearly', $plan->price_yearly ?? '') . '" min="0" step="1000">
                    </div>
                    
                    <div class="form-group">
                        <label for="setup_fee">Setup Fee (IDR)</label>
                        <input type="number" name="setup_fee" id="setup_fee" class="form-control" 
                               value="' . set_value('setup_fee', $plan->setup_fee ?? '0') . '" min="0" step="1000">
                    </div>
                    
                    <div class="form-group">
                        <label for="sort_order">Sort Order</label>
                        <input type="number" name="sort_order" id="sort_order" class="form-control" 
                               value="' . set_value('sort_order', $plan->sort_order ?? '0') . '" min="0">
                    </div>
                    
                    <div class="form-group">
                        <label for="status">Status *</label>
                        <select name="status" id="status" class="form-control" required>
                            <option value="active" ' . set_select('status', 'active', $plan->status == 'active') . '>Active</option>
                            <option value="inactive" ' . set_select('status', 'inactive', $plan->status == 'inactive') . '>Inactive</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="admin-card">
            <div class="card-header">
                <h2><i class="fas fa-list"></i> Features</h2>
            </div>
            <div style="padding: 2rem;">
                <div class="form-group">
                    <label for="features">Features (one per line)</label>
                    <textarea name="features" id="features" class="form-control" rows="6" 
                              placeholder="DDoS Protection&#10;Daily Backups&#10;24/7 Support&#10;Free Domain">' . set_value('features', implode("\n", $features)) . '</textarea>
                    <small class="form-text">Enter each feature on a new line</small>
                </div>
            </div>
        </div>
        
        <div class="plan-stats" style="margin-bottom: 2rem;">
            <div class="admin-card">
                <div class="card-header">
                    <h2><i class="fas fa-chart-bar"></i> Plan Statistics</h2>
                </div>
                <div style="padding: 2rem;">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">Plan ID:</span>
                            <span class="stat-value">#' . $plan->id . '</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Created:</span>
                            <span class="stat-value">' . (isset($plan->created_at) ? date('M j, Y', strtotime($plan->created_at)) : 'N/A') . '</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Last Updated:</span>
                            <span class="stat-value">' . (isset($plan->updated_at) ? date('M j, Y H:i', strtotime($plan->updated_at)) : 'N/A') . '</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="form-actions">
            <a href="' . base_url('admin/server_plans') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Plans
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Update Plan
            </button>
        </div>
    </form>';
} else {
    $content .= '<div class="alert alert-danger">Server plan not found.</div>';
}

$content .= '<style>
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}
.form-group.full-width {
    grid-column: 1 / -1;
}
.form-actions {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}
.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
}
.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
}
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}
.stat-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}
.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}
.stat-value {
    color: white;
    font-weight: 600;
}
</style>';

$data['content'] = $content;
$this->load->view('admin/admin_template', $data);
?> 