<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Category extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('Server_model');
        $this->load->helper(['url', 'asset']);
        $this->load->library('session');
    }

    public function index($slug = null) {
        if (!$slug) {
            redirect(base_url());
        }

        $category = $this->Server_model->get_category_by_slug($slug);

        if (!$category) {
            show_404();
        }

        $data['category'] = $category;
        $data['plans'] = $this->Server_model->get_plans($category->id);
        $data['categories'] = $this->Server_model->get_categories(); // For header

        $this->load->view('pages/category_plans', $data);
    }
}