-- Dopminer Complete Database Schema
-- Drop existing database if exists and create new one
DROP DATABASE IF EXISTS toko_dopminer;
CREATE DATABASE toko_dopminer;
USE toko_dopminer;

-- Users table (existing with role field)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    phone VARCHAR(20),
    balance DECIMAL(15,2) DEFAULT 0.00,
    role ENUM('user', 'admin') DEFAULT 'user',
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Server Categories table
CREATE TABLE server_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    type ENUM('game', 'vps') NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Server Plans table
CREATE TABLE server_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    specifications JSON, -- CPU, RAM, Storage, etc.
    price_monthly DECIMAL(10,2) NOT NULL,
    price_quarterly DECIMAL(10,2),
    price_yearly DECIMAL(10,2),
    setup_fee DECIMAL(10,2) DEFAULT 0.00,
    max_slots INT DEFAULT NULL,
    features JSON, -- List of features
    status ENUM('active', 'inactive') DEFAULT 'active',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES server_categories(id) ON DELETE CASCADE
);

-- Orders table
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    plan_id INT NOT NULL,
    order_number VARCHAR(20) UNIQUE NOT NULL,
    server_name VARCHAR(100) NOT NULL,
    billing_cycle ENUM('monthly', 'quarterly', 'yearly') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    setup_fee DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL,
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    server_status ENUM('pending', 'setting_up', 'active', 'suspended', 'terminated') DEFAULT 'pending',
    expires_at DATETIME,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES server_plans(id) ON DELETE CASCADE
);

-- Servers table (active servers)
CREATE TABLE servers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    user_id INT NOT NULL,
    plan_id INT NOT NULL,
    server_name VARCHAR(100) NOT NULL,
    server_ip VARCHAR(45),
    server_port INT,
    control_panel_url VARCHAR(255),
    control_panel_username VARCHAR(100),
    control_panel_password VARCHAR(255),
    game_config JSON, -- Game-specific configurations
    status ENUM('active', 'suspended', 'maintenance', 'terminated') DEFAULT 'active',
    auto_renew BOOLEAN DEFAULT TRUE,
    next_due_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES server_plans(id) ON DELETE CASCADE
);

-- Transactions table (enhanced)
CREATE TABLE transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    order_id INT DEFAULT NULL,
    transaction_id VARCHAR(50) UNIQUE NOT NULL,
    type ENUM('topup', 'purchase', 'renewal', 'refund', 'bonus') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    balance_before DECIMAL(15,2) NOT NULL,
    balance_after DECIMAL(15,2) NOT NULL,
    status ENUM('pending', 'success', 'failed', 'cancelled') DEFAULT 'pending',
    payment_method ENUM('balance', 'bank_transfer', 'ewallet', 'credit_card') DEFAULT 'balance',
    payment_proof VARCHAR(255),
    description TEXT,
    processed_by INT DEFAULT NULL,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL,
    FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Support Tickets table (enhanced)
CREATE TABLE support_tickets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    order_id INT DEFAULT NULL,
    ticket_number VARCHAR(20) UNIQUE NOT NULL,
    subject VARCHAR(200) NOT NULL,
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
    category ENUM('general', 'billing', 'technical', 'server_issue', 'abuse') DEFAULT 'general',
    status ENUM('open', 'in_progress', 'waiting_customer', 'resolved', 'closed') DEFAULT 'open',
    assigned_to INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL
);

-- Ticket Messages table
CREATE TABLE ticket_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ticket_id INT NOT NULL,
    user_id INT NOT NULL,
    message TEXT NOT NULL,
    is_internal BOOLEAN DEFAULT FALSE,
    attachments JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ticket_id) REFERENCES support_tickets(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Admin Settings table
CREATE TABLE admin_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Activity Logs table
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT DEFAULT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert default admin user
INSERT INTO users (username, email, password, full_name, role, balance) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', 'admin', 0.00);

-- Insert sample server categories
INSERT INTO server_categories (name, slug, description, icon, type, sort_order) VALUES
('FiveM Server', 'fivem', 'GTA V FiveM multiplayer servers with custom mods and scripts', 'fivem-logo.png', 'game', 1),
('SA-MP Server', 'samp', 'San Andreas Multiplayer servers for GTA San Andreas', 'samp-logo.png', 'game', 2),
('RedM Server', 'redm', 'Red Dead Redemption 2 multiplayer servers', 'redm-logo.png', 'game', 3),
('VPS Linux', 'vps-linux', 'Virtual Private Servers with Linux OS', 'linux-icon.png', 'vps', 4),
('VPS Windows', 'vps-windows', 'Virtual Private Servers with Windows OS', 'windows-icon.png', 'vps', 5);

-- Insert sample server plans
INSERT INTO server_plans (category_id, name, slug, description, specifications, price_monthly, price_quarterly, price_yearly, max_slots, features) VALUES
-- FiveM Plans
(1, 'FiveM Starter', 'fivem-starter', 'Perfect for small communities', '{"cpu": "2 vCPU", "ram": "4 GB", "storage": "20 GB SSD", "bandwidth": "Unlimited"}', 75000, 200000, 750000, 32, '["DDoS Protection", "24/7 Support", "Auto Backup", "Web Panel"]'),
(1, 'FiveM Professional', 'fivem-pro', 'For growing communities', '{"cpu": "4 vCPU", "ram": "8 GB", "storage": "40 GB SSD", "bandwidth": "Unlimited"}', 150000, 400000, 1500000, 64, '["DDoS Protection", "24/7 Support", "Auto Backup", "Web Panel", "Priority Support"]'),
(1, 'FiveM Enterprise', 'fivem-enterprise', 'For large communities', '{"cpu": "6 vCPU", "ram": "16 GB", "storage": "80 GB SSD", "bandwidth": "Unlimited"}', 300000, 800000, 3000000, 128, '["DDoS Protection", "24/7 Support", "Auto Backup", "Web Panel", "Priority Support", "Custom Mods"]'),

-- SA-MP Plans  
(2, 'SA-MP Basic', 'samp-basic', 'Basic SA-MP server hosting', '{"cpu": "1 vCPU", "ram": "2 GB", "storage": "10 GB SSD", "bandwidth": "Unlimited"}', 50000, 135000, 500000, 50, '["DDoS Protection", "24/7 Support", "Web Panel"]'),
(2, 'SA-MP Standard', 'samp-standard', 'Standard SA-MP server hosting', '{"cpu": "2 vCPU", "ram": "4 GB", "storage": "20 GB SSD", "bandwidth": "Unlimited"}', 100000, 270000, 1000000, 100, '["DDoS Protection", "24/7 Support", "Auto Backup", "Web Panel"]'),

-- VPS Plans
(4, 'VPS Basic', 'vps-basic', 'Entry level VPS', '{"cpu": "1 vCPU", "ram": "1 GB", "storage": "20 GB SSD", "bandwidth": "1 TB"}', 100000, 270000, 1000000, NULL, '["Root Access", "DDoS Protection", "24/7 Support"]'),
(4, 'VPS Standard', 'vps-standard', 'Standard VPS for small projects', '{"cpu": "2 vCPU", "ram": "2 GB", "storage": "40 GB SSD", "bandwidth": "2 TB"}', 200000, 540000, 2000000, NULL, '["Root Access", "DDoS Protection", "24/7 Support", "Weekly Backup"]');

-- Insert admin settings
INSERT INTO admin_settings (setting_key, setting_value, setting_type, description) VALUES
('site_name', 'Dopminer', 'text', 'Website name'),
('site_description', 'Professional Game Server & VPS Hosting', 'text', 'Website description'),
('currency', 'IDR', 'text', 'Default currency'),
('tax_rate', '0', 'number', 'Tax rate percentage'),
('min_topup', '10000', 'number', 'Minimum top-up amount'),
('max_topup', '10000000', 'number', 'Maximum top-up amount'),
('auto_setup', 'false', 'boolean', 'Automatic server setup'),
('maintenance_mode', 'false', 'boolean', 'Maintenance mode');

-- Insert sample ticket for demo
INSERT INTO support_tickets (user_id, ticket_number, subject, priority, category, status) VALUES
(1, 'TKT-000001', 'Welcome to Dopminer Support', 'normal', 'general', 'resolved');

INSERT INTO ticket_messages (ticket_id, user_id, message) VALUES
(1, 1, 'Welcome to Dopminer! This is a sample support ticket. Our team is ready to help you 24/7.'); 