<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Auth extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('User_model');
        $this->load->library(['form_validation', 'session']);
        $this->load->helper(['url', 'form', 'asset']);
    }

    public function login() {
        if ($this->session->userdata('user_id')) {
            redirect('dashboard');
        }

        if ($this->input->post()) {
            $this->form_validation->set_rules('email', 'Email', 'required|valid_email');
            $this->form_validation->set_rules('password', 'Password', 'required');

            if ($this->form_validation->run()) {
                $email = $this->input->post('email');
                $password = $this->input->post('password');
                
                try {
                    $user = $this->User_model->login($email, $password);
                    
                    if ($user) {
                        // Set session data
                        $this->session->set_userdata([
                            'user_id' => $user->id,
                            'username' => $user->username,
                            'email' => $user->email,
                            'role' => $user->role
                        ]);
                        
                        redirect('dashboard');
                    } else {
                        $this->session->set_flashdata('error', 'Email atau password salah');
                    }
                } catch (Exception $e) {
                    $this->session->set_flashdata('error', 'Terjadi kesalahan database: ' . $e->getMessage());
                }
            } else {
                $this->session->set_flashdata('error', strip_tags(validation_errors()));
            }
        }

        $data['error'] = $this->session->flashdata('error');
        $this->load->view('auth/login', $data);
    }

    public function register() {
        if ($this->session->userdata('user_id')) {
            redirect('dashboard');
        }

        if ($this->input->post()) {
            $this->form_validation->set_rules('username', 'Username', 'required|min_length[4]|is_unique[users.username]');
            $this->form_validation->set_rules('email', 'Email', 'required|valid_email|is_unique[users.email]');
            $this->form_validation->set_rules('password', 'Password', 'required|min_length[6]');
            $this->form_validation->set_rules('confirm_password', 'Konfirmasi Password', 'required|matches[password]');
            $this->form_validation->set_rules('full_name', 'Nama Lengkap', 'required');

            if ($this->form_validation->run()) {
                $data = [
                    'username' => $this->input->post('username'),
                    'email' => $this->input->post('email'),
                    'password' => $this->input->post('password'),
                    'full_name' => $this->input->post('full_name')
                ];
                
                try {
                    $user_id = $this->User_model->register($data);
                    
                    if ($user_id) {
                        $this->session->set_flashdata('success', 'Registrasi berhasil! Silakan login.');
                        redirect('auth/login');
                    } else {
                        $this->session->set_flashdata('error', 'Terjadi kesalahan saat mendaftar. Silakan coba lagi.');
                    }
                } catch (Exception $e) {
                    $this->session->set_flashdata('error', 'Terjadi kesalahan database: ' . $e->getMessage());
                }
            } else {
                $this->session->set_flashdata('error', strip_tags(validation_errors()));
            }
        }

        $data['error'] = $this->session->flashdata('error');
        $this->load->view('auth/register', $data);
    }

    public function logout() {
        $this->session->sess_destroy();
        redirect('auth/login');
    }

    public function forgot_password() {
        // TODO: Implement forgot password functionality
        $this->load->view('auth/forgot_password');
    }
} 