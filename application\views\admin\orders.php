<?php
defined('BASEPATH') OR exit('No direct script access allowed');
// Copy template from admin_template.php but with orders content
$data['title'] = $title ?? 'Orders';
$data['content'] = '
<div class="page-header">
    <h1>Order Management</h1>
    <p>View and manage all customer orders</p>
</div>

<div class="admin-card">
    <div class="card-header">
        <h2><i class="fas fa-shopping-cart"></i> All Orders</h2>
    </div>
    <div class="table-responsive">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>Order ID</th>
                    <th>User</th>
                    <th>Plan</th>
                    <th>Amount</th>
                    <th>Payment Status</th>
                    <th>Server Status</th>
                    <th>Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>';

if (isset($orders) && !empty($orders)):
    foreach ($orders as $order):
        $data['content'] .= '
                <tr>
                    <td>#' . str_pad($order->id, 5, '0', STR_PAD_LEFT) . '</td>
                    <td>' . ($order->username ?? 'N/A') . '<br><small>' . ($order->email ?? '') . '</small></td>
                    <td>' . ($order->plan_name ?? 'N/A') . '</td>
                    <td>Rp ' . number_format($order->amount ?? 0, 0, ',', '.') . '</td>
                    <td>
                        <span class="badge ' . (isset($order->payment_status) && $order->payment_status == 'paid' ? 'success' : 'warning') . '">
                            ' . ucfirst($order->payment_status ?? 'pending') . '
                        </span>
                    </td>
                    <td>
                        <span class="badge ' . (isset($order->status) && $order->status == 'active' ? 'success' : 'info') . '">
                            ' . ucfirst($order->status ?? 'pending') . '
                        </span>
                    </td>
                    <td>' . (isset($order->created_at) ? date('d M Y', strtotime($order->created_at)) : 'N/A') . '</td>
                    <td>
                        <div class="action-buttons">
                            <a href="' . base_url('admin/order_details/' . $order->id) . '" class="btn btn-sm btn-primary">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </td>
                </tr>';
    endforeach;
else:
    $data['content'] .= '
                <tr>
                    <td colspan="8" style="text-align: center; color: rgba(255, 255, 255, 0.5);">
                        No orders found
                    </td>
                </tr>';
endif;

$data['content'] .= '
            </tbody>
        </table>
    </div>
</div>';

// Load admin template with orders content
$this->load->view('admin/admin_template', $data);
?> 